package com.doorbit.projectconfigurator.bff.infra.config

import com.doorbit.projectconfigurator.bff.core.domain.extension.WithLogger
import com.doorbit.projectconfigurator.bff.core.domain.extension.e
import graphql.GraphQLError
import graphql.GraphqlErrorBuilder
import graphql.schema.DataFetchingEnvironment
import io.micrometer.core.instrument.MeterRegistry
import org.springframework.core.env.Environment
import org.springframework.graphql.execution.DataFetcherExceptionResolverAdapter
import org.springframework.graphql.execution.ErrorType
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component

@Component
class ErrorHandler(
    private val meterRegistry: MeterRegistry,
    private val environment: Environment,
) : DataFetcherExceptionResolverAdapter() {

    override fun resolveToSingleError(ex: Throwable, env: DataFetchingEnvironment): GraphQLError {
        return resolveToGenericError(ex, env)
    }

    private fun resolveToGenericError(ex: Throwable, env: DataFetchingEnvironment): GraphQLError {
        LOGGER.e(ex) { "Unexpected error in operation ${env.operationDefinition.name}" }
        return GraphqlErrorBuilder.newError()
            .message("The service is currently not available. Please try again later.")
            .errorType(ErrorType.INTERNAL_ERROR)
            .path(env.executionStepInfo.path)
            .build()
    }

    companion object : WithLogger()

}