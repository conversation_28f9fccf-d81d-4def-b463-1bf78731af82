extend type Query {

    """
    Eigentliche Konfigurationstabelle mit den Leistungspositionen und der Pflege.
    https://www.figma.com/file/dukBAGA01jOWXf8JC42gnL/Mockups?type=design&node-id=25-52038&mode=design&t=8ijFKfkIFAJbWxfC-0
    """
    configurationTable(configurationId: String!, displayContext: DisplayContext!): ConfigurationTable!

    """
    Liste möglicher auswählbarer Datenbankversionen, für die eine Konfiguration erstellt werden kann.
    """
    databaseVersions: [DatabaseVersion!]!

    leistungDetails(configurationId: String!, leistungId: String!, displayContext: DisplayContext!): LeistungDetails!

    leistungsPositionDefaults(configurationId: String!): LeistungsPositionDefaults!

    """
    Gibt die Massedaten des Gebäudes zurück. Es kann auch "keine Massedaten" geben, z. B. weil (noch) kein Gebäude existiert.
    """
    massedaten(projectId: String!): [Massedatum!]
}

extend type Mutation {

    """
    Importiert die Produktdatenbank und gibt die Datenbank Id zurück.
    """
    importProductDatabase(versionName: String!): String!

    """
    Neue Konfiguration erstellen mit Anzeigename.
    Modulname muss immer ausgewählt werden.
    ConfigurationId wird zurückgegeben.
    Mit dem optionalen Feld schnellKonfiguration kann eine Schnellkonfiguration gewählt werden. Ansonsten null lassen.
    """
    createConfiguration(projectId: String!, productDatabaseId: String!, name: String!, schnellKonfiguration: SchnellkonfigurationType): String!

    copyConfiguration(projectId: String!, configurationId: String!, name: String!): String!

    """
    Archiviert eine Konfiguration. Sie wird nicht mehr ausgegeben.
    """
    archiveConfiguration(configurationId: String!): Boolean!

    """
    Hakt einen Leistung an oder ab und kalkuliert neu.
    """
    selectLeistung(configurationId: String!, leistungId: String!, selection: Boolean!, displayContext: DisplayContext!): ConfigurationTable!

    """
    Hakt eine Leistungsposition an oder ab und kalkuliert neu.
    """
    selectLeistungsPosition(configurationId: String!, leistungId: String!, leistungsPositionId: String!, selection: Boolean!, displayContext: DisplayContext!): ConfigurationTable!

    """
    Wählt aus wer die Arbeit durchführt und kalkuliert neu.
    """
    changeExecution(configurationId: String!, leistungId: String!, execution: ExecutionType!, displayContext: DisplayContext!): ConfigurationTable!

    """
    Legt einen neuen Leistung an und ordnet ihn der Komponente zu.
    """
    createLeistung(configurationId: String!, komponenteId: String!, payload: LeistungInput!, displayContext: DisplayContext!): ConfigurationTable!

    deleteLeistung(configurationId: String!, leistungId: String!, displayContext: DisplayContext!): ConfigurationTable!

    deleteLeistungsPosition(configurationId: String!, leistungId: String!, leistungsPositionId: String!, displayContext: DisplayContext!): ConfigurationTable!

    """
    Legt eine neue Leistungsposition an und ordnet sie dem Leistung zu.
    """
    createLeistungsPosition(configurationId: String!, leistungId: String!, payload: LeistungsPositionInput!, displayContext: DisplayContext!): ConfigurationTable!

    """
    UnitPrice und/oder Margin in Prozent als 0.01 für 1% und/oder Stückzahl einer Leistungsposition ändern.
    Führt sofort zu einem neuen Kalkulationsergebnis, das per Abruf von ConfigurationTable abgerufen werden kann.
    Sollte der EP noch nicht selektiert sein so wird er nun selektiert.
    """
    configureLeistungsPosition(configurationId: String!, leistungId: String!, leistungsPositionId: String!, unitPrice: Float, vatRatePercent: Float, amount: Float, margin: Float, securitySurcharge: Float, notes: String, displayContext: DisplayContext!) : ConfigurationTable!
}

enum SchnellkonfigurationType {
    S,M,L
}

type Massedatum {
    key: String!
    icon: String!
    displayName: [UIElementTranslation!]!
    unitDisplayName: String!

    """
    Either one of those is filled
    """
    valueDouble: Float
    valueInt: Int
    valueString: String
}

type LeistungsPositionDefaults {
    vatRatePercent: Float!
    marginPercent: Float!
    securitySurchargePercent: Float!
    existingUnits: [String!]!
}

input LeistungInput {

    leistungType: LeistungType!
    displayName: String!
    offerPrice: Float!
    globallyAvailable: Boolean!

}

input LeistungsPositionInput {
    displayName: String!
    unit: String!
    unitPrice: Float!
    vatRatePercent: Float!
    marginPercent: Float!
    securitySurchargePercent: Float!
    amount: Float!
    globallyAvailable: Boolean!
    notes: String
}

enum ExecutionType {
    CLIENT
    CONTRACTOR
}

type CalculationResult {
    """
    Gesamtpreis der Konfiguration
    """
    offerPriceGross: Float!
    offerPriceNet: Float!
    vatAmount: Float!

    calculationsByModule: [CalculationByModule!]!
}

type CalculationByModule {
    moduleName: String!

    netCosts: Float!
    securitySurcharge: Float!
    margin: Float!
    offerPriceGross: Float!
    offerPriceNet: Float!
    vatAmount: Float!
}

type DatabaseVersion {
    databaseId: String!
    version: String!
    createdAt: String!
}

"""
Die Tabelle ist im Grunde vollständig konfigurierbar, die Verzweifungstiefe ist vorgegeben.
Die Tabelle startet ab den "ServiceDirectoryCategories".
Spalten und Zeilen der Tabelle selbst stammen jeweils vollständig aus dem Leistungsverzeichnis / Config im Backoffice.
"""
type ConfigurationTable {

    configurationId: String!
    configurationName: String!
    databaseVersion: DatabaseVersion!

    """
    Dach, Fassade, Keller, etc.
    """
    vergabeEinheiten: [VergabeEinheit!]!

    calculationResult: CalculationResult!
}

"""
Das Konzept ist angedacht. Ob die Unterscheidbarkeit wirklich kommt ist offen.
Bis dahin versuchen wir es schonmal mitzusupporten ohne einen großen Aufwand zu machen.
"""
enum DisplayContext {
    """
    Im Betrachtungsmodus Backoffice werden alle Informationen angezeigt.
    """
    BACKOFFICE,

    """
    Der Betrachtungsmodus Endkunde zeigt nur die Informationen, die für den Endkunden relevant sind und das möglichst vereinfacht.
    """
    END_CUSTOMER
}

type SideNavigation {

    configurationTable: ConfigurationTable!
}

type VergabeEinheit {

    id: String!

    displayName: [UIElementTranslation!]!

    moduleName: String!

    """
    Dach
    -- Vorbereitung des Dachstuhls
    -- Dachdämmung, etc.
    """
    komponenten: [VergabeEinheitKomponente!]!

    """
    Vollständigkeit der Pflege dieser Vergabeeinheit
    """
    completenessState: CompletenessState!

    """
    Ob vollständig gepflegt oder nicht, die Pflege kann Warnungen enthalten
    """
    hasWarnings: Boolean!

    """
    Angebotspreis brutto
    """
    offerPriceGross: Float!

    offerPriceNet: Float!

    """
    Plankosten netto
    """
    netCosts: Float!

    margin: Float!

    vatAmount: Float!

    securitySurcharge: Float!
}

type VergabeEinheitKomponente {

    id: String!

    displayName: [UIElementTranslation!]!

    leistung: [Leistung!]!

    """
    Vollständigkeit der Pflege dieser Vergabeeinheit
    """
    completenessState: CompletenessState!

    """
    Ob vollständig gepflegt oder nicht, die Pflege kann Warnungen enthalten
    """
    hasWarnings: Boolean!


    """
    Angebotspreis brutto
    """
    offerPriceGross: Float!
    """
    Vergabebudget netto
    """
    netCosts: Float!

    margin: Float!

    vatAmount: Float!

    securitySurcharge: Float!
}

type Leistung {

    """
    Eindeutige Id des Leistungs
    """
    id: String!

    type: LeistungType

    """
    Ob der Leistung angehakt ist oder nicht.
    """
    isSelected: Boolean!

    cells: [KeyValueItem!]!

    leistungsPositionen: [LeistungsPosition!]!
}

type LeistungsPosition {
    id: String!
    cells: [KeyValueItem!]!
}

type LeistungDetails {
    id: String!

    isSelected: Boolean!

    type: LeistungType!
    displayName: String!
    longDisplayName: String
    longDescription: String
    imageUrls: [String!]!
    isCustomCreated: Boolean

    table: LeistungDetailTable!

    leistungsPositionen: [LeistungsPosition!]!
}

type LeistungDetailTable {
    uValue: Float
    manufacturer: String
    factSheetUrl: String
    eligible: Boolean
    execution: ExecutionType
    offerPriceGross: Float
}

enum LeistungType {
    ADD_ON,
    ALTERNATIVE,
    ZERO_VARIANT
}

type KeyValueItem {

    key: String!

    """
    0..1 of these is set
    """
    intValue: Int
    doubleValue: Float
    stringValue: String
    booleanValue: Boolean
}

type ConfigurationTableColumnDescription {
    key: String!
    headerDisplayName: [UIElementTranslation!]!
    columnType: ConfigurationTableColumnType!
}

enum ConfigurationTableColumnType {
    PRICE,
    TEXT,
    """
    Combo aus Integer / Double und Unit "Stck"
    """
    PIECES,
    """
    Wer führt die Arbeit aus? Dropdown CLIENT, CONTRACTOR. Siehe ExecutionType
    """
    EXECUTION,

    # Schalter (boolean)
    TOGGLE
}

enum CompletenessState {
    COMPLETELY_CONFIGURED
    PARTIALLY_CONFIGURED
    NOT_CONFIGURED
}
