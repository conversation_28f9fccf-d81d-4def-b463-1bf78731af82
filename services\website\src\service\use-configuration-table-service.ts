import { inject, InjectionKey, provide, ref } from 'vue';
import { DConfigTableFragment, useDLeistungSelectionMutation } from '@/adapter/graphql/generated/graphql';
import { useI18n } from 'vue-i18n';
import { getTranslation } from '@/utility/get-translation';
import { LeistungWithParentNames } from '@/components/content/leistung/LeistungWithParentNames';
import { addSequences } from './addSequences';

export type ConfigurationTableService = ReturnType<typeof initializeConfigurationTableService>;

const configurationTableServiceKey = Symbol('configurationTableService') as InjectionKey<ConfigurationTableService>;

const pastelColors = [
  // keep comment so prettier does not put everything in one line
  '#ffb3ba',
  '#ffdfba',
  '#ffffba',
  '#baffc9',
  '#bae1ff'
];

export interface ModuleConfig {
  name: string;
  color: string;
  offerPriceGross: number;
  isActive: boolean;
}

export function initializeConfigurationTableService(provideFn: typeof provide) {
  const { locale } = useI18n();
  const configurationTable = ref<DConfigTableFragment | null>(null);
  const leistungenWithParentNames = ref<LeistungWithParentNames[]>([]);
  const moduleConfigs = ref<ModuleConfig[]>([]);
  const moduleToColorMap = ref<{ [moduleName: string]: string }>({});
  const collapse = ref<null | Date>(null);

  const {
    mutate: changeLeistungSelection,
    error: leistungSelectionChangeError,
    onDone: leistungSelectionChanged
  } = useDLeistungSelectionMutation();

  leistungSelectionChanged((res) => {
    if (res?.data) {
      setConfigTable(res.data.selectLeistung as DConfigTableFragment);
    }
  });

  function setConfigTable(table: DConfigTableFragment | null) {
    configurationTable.value = table ? addSequences(table) : null;

    leistungenWithParentNames.value =
      configurationTable.value?.vergabeEinheiten.flatMap((ve) =>
        ve.komponenten.flatMap((komponente) =>
          komponente.leistung.map((leistung) => ({
            vergabeEinheitName: getTranslation(locale.value, ve.displayName),
            komponenteName: getTranslation(locale.value, komponente.displayName),
            moduleName: ve.moduleName,
            leistungId: leistung.id
          }))
        )
      ) || [];

    moduleConfigs.value =
      configurationTable.value?.calculationResult.calculationsByModule.map(({ moduleName, offerPriceGross }, i) => {
        const existing = moduleConfigs.value.find((config) => config.name === moduleName);
        return {
          name: moduleName,
          offerPriceGross,
          color: pastelColors[i % pastelColors.length],
          isActive: existing ? existing.isActive : true
        };
      }) || [];
    moduleToColorMap.value = moduleConfigs.value.reduce((acc, module) => ({ ...acc, [module.name]: module.color }), {});
  }

  const configurationTableService = {
    configurationTable,
    setConfigTable,
    changeLeistungSelection,
    leistungenWithParentNames,
    leistungSelectionChangeError,
    moduleConfigs,
    moduleToColorMap,
    collapse
  };
  provideFn<ConfigurationTableService>(configurationTableServiceKey, configurationTableService);
  return configurationTableService;
}

export function useConfigurationTableService(): ConfigurationTableService {
  return inject<ConfigurationTableService>(configurationTableServiceKey)!;
}
