mutation dLeistungspositionUpdate(
  $configurationId: String!
  $leistungId: String!
  $leistungsPositionId: String!
  $unitPrice: Float
  $vatRatePercent: Float
  $amount: Float
  $margin: Float
  $securitySurcharge: Float
  $notes: String
) {
  configureLeistungsPosition(
    configurationId: $configurationId
    leistungId: $leistungId
    leistungsPositionId: $leistungsPositionId
    unitPrice: $unitPrice
    vatRatePercent: $vatRatePercent
    amount: $amount
    margin: $margin
    securitySurcharge: $securitySurcharge
    notes: $notes
    displayContext: BACKOFFICE
  ) {
    ...DConfigTable
  }
}
