package com.doorbit.projectconfigurator.bff.infra.api

import com.doorbit.projectconfigurator.bff.core.domain.extension.WithLogger
import com.doorbit.projectconfigurator.bff.core.domain.extension.d
import com.doorbit.projectconfigurator.bff.core.domain.extension.w
import com.doorbit.projectconfigurator.bff.infra.adapter.ProjectConfiguratorApiAdapter
import com.doorbit.projectconfigurator.bff.infra.api.dto.*
import com.doorbit.projectconfigurator.projectconfigurator.domain.LeistungId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProductDatabaseId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProjectConfigurationId
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.LeistungsPositionId
import org.bson.types.ObjectId
import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.MutationMapping
import org.springframework.stereotype.Controller
import java.math.BigDecimal

@Controller
class ProjectConfiguratorMutations(
    private val projectConfiguratorApiAdapter: ProjectConfiguratorApiAdapter,
    private val configurationTableService: ConfigurationTableService,
) {

    @MutationMapping
    fun importProductDatabase(@Argument versionName: String): ProductDatabaseId {
        LOGGER.d { "importProductDatabase: versionName=$versionName"}
        return projectConfiguratorApiAdapter.importProductDatabase(versionName)
    }

    @MutationMapping
    fun archiveConfiguration(
        @Argument configurationId: String,
    ): Boolean{
        LOGGER.d { "archiveConfiguration: configurationId=$configurationId"}
        projectConfiguratorApiAdapter.archiveConfiguration(ObjectId(configurationId))
        return true
    }

    @MutationMapping
    fun createConfiguration(
        @Argument projectId: String,
        @Argument productDatabaseId: ProductDatabaseId,
        @Argument name: String,
        @Argument schnellKonfiguration: SchnellkonfigurationType?
    ): ProjectConfigurationId {
        LOGGER.d { "createConfiguration: projectId=$projectId, productDatabaseId=$productDatabaseId, name=$name, schnellKonfiguration=$schnellKonfiguration"}
        LOGGER.w { "[METRIC] CREATE_PROJECT_CONFIGURATION_CONFIG - SCHNELL_KONFIGURATION: $schnellKonfiguration" }
        return projectConfiguratorApiAdapter.createConfiguration(projectId, productDatabaseId, name, schnellKonfiguration)
    }

    @MutationMapping
    fun copyConfiguration(
        @Argument projectId: String,
        @Argument configurationId: String,
        @Argument name: String,
    ): ProjectConfigurationId {
        LOGGER.d { "copyConfiguration: configurationId=$configurationId, name=$name"}
        return projectConfiguratorApiAdapter.copyConfiguration(projectId, ObjectId(configurationId), name)
    }

    @MutationMapping
    fun selectLeistung(
        @Argument configurationId: String,
        @Argument leistungId: String,
        @Argument selection: Boolean,
        @Argument displayContext: DisplayContext,
    ): ConfigurationTable {
        LOGGER.d { "selectLeistung: configurationId=$configurationId, leistungId=$leistungId, selection=$selection"}
        projectConfiguratorApiAdapter.selectLeistung(ObjectId(configurationId), leistungId, selection)
        return configurationTableService.findConfigurationTable(configurationId, displayContext)!!
    }

    @MutationMapping
    fun selectLeistungsPosition(
        @Argument configurationId: String,
        @Argument leistungId: LeistungId,
        @Argument leistungsPositionId: LeistungsPositionId,
        @Argument selection: Boolean,
        @Argument displayContext: DisplayContext,
    ) : ConfigurationTable {
        LOGGER.d { "selectLeistungsPosition: configurationId=$configurationId, leistungsPositionId=$leistungsPositionId, selection=$selection"}
        projectConfiguratorApiAdapter.selectLeistungsPosition(ObjectId(configurationId), leistungId, leistungsPositionId, selection)
        return configurationTableService.findConfigurationTable(configurationId, displayContext)!!
    }

    @MutationMapping
    fun createLeistung(
        @Argument configurationId: String,
        @Argument komponenteId: String,
        @Argument payload: LeistungInput,
        @Argument displayContext: DisplayContext,
    ): ConfigurationTable {
        LOGGER.d { "createLeistung: configurationId=$configurationId, komponenteId=$komponenteId, payload=$payload"}
        projectConfiguratorApiAdapter.createLeistung(ObjectId(configurationId), komponenteId, payload)
        return configurationTableService.findConfigurationTable(configurationId, displayContext)!!
    }

    @MutationMapping
    fun createLeistungsPosition(
        @Argument configurationId: String,
        @Argument leistungId: LeistungId,
        @Argument payload: LeistungsPositionInput,
        @Argument displayContext: DisplayContext,
    ): ConfigurationTable {
        LOGGER.d { "createLeistungsPosition: configurationId=$configurationId, leistungId=$leistungId, payload=$payload"}
        projectConfiguratorApiAdapter.createLeistungsPosition(ObjectId(configurationId), leistungId, payload)
        return configurationTableService.findConfigurationTable(configurationId, displayContext)!!
    }

    @MutationMapping
    fun changeExecution(
        @Argument configurationId: String,
        @Argument leistungId: String,
        @Argument execution: ExecutionType,
        @Argument displayContext: DisplayContext,
    ): ConfigurationTable {
        LOGGER.d { "changeExecution: configurationId=$configurationId, leistungId=$leistungId, execution=$execution"}
        projectConfiguratorApiAdapter.changeExecution(ObjectId(configurationId), leistungId, execution)
        return configurationTableService.findConfigurationTable(configurationId, displayContext)!!
    }

    @MutationMapping
    fun configureLeistungsPosition(
        @Argument configurationId: String,
        @Argument leistungId: LeistungId,
        @Argument leistungsPositionId: LeistungsPositionId,
        @Argument unitPrice: BigDecimal?,
        @Argument vatRatePercent: BigDecimal?,
        @Argument amount: BigDecimal?,
        @Argument margin: BigDecimal?,
        @Argument securitySurcharge: BigDecimal?,
        @Argument displayContext: DisplayContext,
        @Argument notes: String?,
    ): ConfigurationTable {
        LOGGER.d { "configureLeistungsPosition: configurationId=$configurationId, leistungId=$leistungId, leistungsPositionId=$leistungsPositionId, unitPrice=$unitPrice, vatRatePercent=$vatRatePercent, amount=$amount, margin=$margin, securitySurcharge=$securitySurcharge"}
        projectConfiguratorApiAdapter.configureLeistungsPosition(ObjectId(configurationId), leistungId, leistungsPositionId, unitPrice, vatRatePercent, amount, margin, notes, securitySurcharge)
        return configurationTableService.findConfigurationTable(configurationId, displayContext)!!
    }

    @MutationMapping
    fun deleteLeistung(
        @Argument configurationId: String,
        @Argument leistungId: LeistungId,
        @Argument displayContext: DisplayContext,
    ): ConfigurationTable {
        LOGGER.d { "deleteLeistung: configurationId=$configurationId, leistungId=$leistungId"}
        projectConfiguratorApiAdapter.deleteLeistung(ObjectId(configurationId), leistungId)
        return configurationTableService.findConfigurationTable(configurationId, displayContext)!!
    }

    @MutationMapping
    fun deleteLeistungsPosition(
        @Argument configurationId: String,
        @Argument leistungId: LeistungId,
        @Argument leistungsPositionId: LeistungsPositionId,
        @Argument displayContext: DisplayContext,
    ): ConfigurationTable {
        LOGGER.d { "deleteLeistungsPosition: configurationId=$configurationId, leistungId=$leistungId, leistungsPositionId=$leistungsPositionId"}
        projectConfiguratorApiAdapter.deleteLeistungsPosition(ObjectId(configurationId), leistungId, leistungsPositionId)
        return configurationTableService.findConfigurationTable(configurationId, displayContext)!!
    }

    companion object : WithLogger()

}