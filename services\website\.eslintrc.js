module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: [
    'plugin:vue/vue3-recommended',
    'eslint:recommended',
    '@vue/eslint-config-typescript/recommended',
    '@vue/eslint-config-prettier'
  ],
  rules: {
    // 'vue/html-indent': 'off',
    // 'vue/first-attribute-linebreak': 'off',
    // 'vue/html-closing-bracket-newline': 'off',
    // 'vue/html-closing-bracket-spacing': 'off',
    // 'vue/singleline-html-element-content-newline': 'off',
    // '@typescript-eslint/no-unused-vars': 'off',
    // '@typescript-eslint/no-non-null-assertion': 'off',
    // 'vue/multiline-html-element-content-newline': 'off',
    // '@typescript-eslint/no-explicit-any': 'off',
  }
};
