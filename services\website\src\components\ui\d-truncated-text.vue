<template>
  <v-tooltip v-if="text?.length" open-on-click :max-width="maxTooltipWidth">
    <span>{{ text }}</span>
    <template #activator="{ props: activatorProps }">
      <div class="truncated-text__activator" v-bind="activatorProps">
        <span class="pr-1">{{ text }}</span>
      </div>
    </template>
  </v-tooltip>
</template>

<script lang="ts" setup>
withDefaults(
  defineProps<{
    text?: string | null;
    maxTooltipWidth?: number;
  }>(),
  {
    text: '',
    maxTooltipWidth: 400
  }
);
</script>

<style lang="scss">
.truncated-text__activator {
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  text-overflow: ellipsis;
  cursor: pointer;
}
</style>
