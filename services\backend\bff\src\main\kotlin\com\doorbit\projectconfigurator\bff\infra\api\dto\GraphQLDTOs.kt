package com.doorbit.projectconfigurator.bff.infra.api.dto

data class CalculationByModule(
    val margin: Double,
    val moduleName: String,
    val netCosts: Double,
    val offerPriceGross: Double,
    val offerPriceNet: Double,
    val securitySurcharge: Double,
    val vatAmount: Double
)

data class CalculationResult(
    val calculationsByModule: List<CalculationByModule>,
    val offerPriceGross: Double,
    val offerPriceNet: Double,
    val vatAmount: Double
)

enum class CompletenessState(val label: String) {
      COMPLETELY_CONFIGURED("COMPLETELY_CONFIGURED"),
      NOT_CONFIGURED("NOT_CONFIGURED"),
      PARTIALLY_CONFIGURED("PARTIALLY_CONFIGURED");
        
  companion object {
    @JvmStatic
    fun valueOfLabel(label: String): CompletenessState? {
      return values().find { it.label == label }
    }
  }
}

data class ConfigurationTable(
    val calculationResult: CalculationResult,
    val configurationId: String,
    val configurationName: String,
    val databaseVersion: DatabaseVersion,
    val vergabeEinheiten: List<VergabeEinheit>
)

data class ConfigurationTableColumnDescription(
    val columnType: ConfigurationTableColumnType,
    val headerDisplayName: List<UiElementTranslation>,
    val key: String
)

enum class ConfigurationTableColumnType(val label: String) {
      EXECUTION("EXECUTION"),
      PIECES("PIECES"),
      PRICE("PRICE"),
      TEXT("TEXT"),
      TOGGLE("TOGGLE");
        
  companion object {
    @JvmStatic
    fun valueOfLabel(label: String): ConfigurationTableColumnType? {
      return values().find { it.label == label }
    }
  }
}

data class DatabaseVersion(
    val createdAt: String,
    val databaseId: String,
    val version: String
)

/**
 * Das Konzept ist angedacht. Ob die Unterscheidbarkeit wirklich kommt ist offen.
 * Bis dahin versuchen wir es schonmal mitzusupporten ohne einen großen Aufwand zu machen.
 */
enum class DisplayContext(val label: String) {
      BACKOFFICE("BACKOFFICE"),
      END_CUSTOMER("END_CUSTOMER");
        
  companion object {
    @JvmStatic
    fun valueOfLabel(label: String): DisplayContext? {
      return values().find { it.label == label }
    }
  }
}

enum class ExecutionType(val label: String) {
      CLIENT("CLIENT"),
      CONTRACTOR("CONTRACTOR");
        
  companion object {
    @JvmStatic
    fun valueOfLabel(label: String): ExecutionType? {
      return values().find { it.label == label }
    }
  }
}

data class KeyValueItem(
    val booleanValue: Boolean?,
    val doubleValue: Double?,
    val intValue: Int?,
    val key: String,
    val stringValue: String?
)

data class Leistung(
    val cells: List<KeyValueItem>,
    val id: String,
    val isSelected: Boolean,
    val leistungsPositionen: List<LeistungsPosition>,
    val type: LeistungType?
)

data class LeistungDetailTable(
    val eligible: Boolean?,
    val execution: ExecutionType?,
    val factSheetUrl: String?,
    val manufacturer: String?,
    val offerPriceGross: Double?,
    val uValue: Double?
)

data class LeistungDetails(
    val displayName: String,
    val id: String,
    val imageUrls: List<String>,
    val isCustomCreated: Boolean?,
    val isSelected: Boolean,
    val leistungsPositionen: List<LeistungsPosition>,
    val longDescription: String?,
    val longDisplayName: String?,
    val table: LeistungDetailTable,
    val type: LeistungType
)

data class LeistungInput(
    val displayName: String,
    val globallyAvailable: Boolean,
    val leistungType: LeistungType,
    val offerPrice: Double
) {
  constructor(args: Map<String, Any>) : this(
      args["displayName"] as String,
      args["globallyAvailable"] as Boolean,
      args["leistungType"] as LeistungType,
      args["offerPrice"] as Double
  )
}

enum class LeistungType(val label: String) {
      ADD_ON("ADD_ON"),
      ALTERNATIVE("ALTERNATIVE"),
      ZERO_VARIANT("ZERO_VARIANT");
        
  companion object {
    @JvmStatic
    fun valueOfLabel(label: String): LeistungType? {
      return values().find { it.label == label }
    }
  }
}

data class LeistungsPosition(
    val cells: List<KeyValueItem>,
    val id: String
)

data class LeistungsPositionDefaults(
    val existingUnits: List<String>,
    val marginPercent: Double,
    val securitySurchargePercent: Double,
    val vatRatePercent: Double
)

data class LeistungsPositionInput(
    val amount: Double,
    val displayName: String,
    val globallyAvailable: Boolean,
    val marginPercent: Double,
    val notes: String? = null,
    val securitySurchargePercent: Double,
    val unit: String,
    val unitPrice: Double,
    val vatRatePercent: Double
) {
  constructor(args: Map<String, Any>) : this(
      args["amount"] as Double,
      args["displayName"] as String,
      args["globallyAvailable"] as Boolean,
      args["marginPercent"] as Double,
      args["notes"] as String?,
      args["securitySurchargePercent"] as Double,
      args["unit"] as String,
      args["unitPrice"] as Double,
      args["vatRatePercent"] as Double
  )
}

data class Massedatum(
    val displayName: List<UiElementTranslation>,
    val icon: String,
    val key: String,
    val unitDisplayName: String,
    val valueDouble: Double?,
    val valueInt: Int?,
    val valueString: String?
)

data class MutationArchiveConfigurationArgs(
    val configurationId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String
  )
}
data class MutationChangeExecutionArgs(
    val configurationId: String,
    val displayContext: DisplayContext,
    val execution: ExecutionType,
    val leistungId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext,
      args["execution"] as ExecutionType,
      args["leistungId"] as String
  )
}
data class MutationConfigureLeistungsPositionArgs(
    val amount: Double? = null,
    val configurationId: String,
    val displayContext: DisplayContext,
    val leistungId: String,
    val leistungsPositionId: String,
    val margin: Double? = null,
    val notes: String? = null,
    val securitySurcharge: Double? = null,
    val unitPrice: Double? = null,
    val vatRatePercent: Double? = null
) {
  constructor(args: Map<String, Any>) : this(
      args["amount"] as Double?,
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext,
      args["leistungId"] as String,
      args["leistungsPositionId"] as String,
      args["margin"] as Double?,
      args["notes"] as String?,
      args["securitySurcharge"] as Double?,
      args["unitPrice"] as Double?,
      args["vatRatePercent"] as Double?
  )
}
data class MutationCopyConfigurationArgs(
    val configurationId: String,
    val name: String,
    val projectId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["name"] as String,
      args["projectId"] as String
  )
}
data class MutationCreateConfigurationArgs(
    val name: String,
    val productDatabaseId: String,
    val projectId: String,
    val schnellKonfiguration: SchnellkonfigurationType? = null
) {
  constructor(args: Map<String, Any>) : this(
      args["name"] as String,
      args["productDatabaseId"] as String,
      args["projectId"] as String,
      args["schnellKonfiguration"] as SchnellkonfigurationType?
  )
}
data class MutationCreateLeistungArgs(
    val configurationId: String,
    val displayContext: DisplayContext,
    val komponenteId: String,
    val payload: LeistungInput
) {
  @Suppress("UNCHECKED_CAST")
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext,
      args["komponenteId"] as String,
      LeistungInput(args["payload"] as Map<String, Any>)
  )
}
data class MutationCreateLeistungsPositionArgs(
    val configurationId: String,
    val displayContext: DisplayContext,
    val leistungId: String,
    val payload: LeistungsPositionInput
) {
  @Suppress("UNCHECKED_CAST")
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext,
      args["leistungId"] as String,
      LeistungsPositionInput(args["payload"] as Map<String, Any>)
  )
}
data class MutationCreateProjectArgs(
    val listingId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["listingId"] as String
  )
}
data class MutationDeleteLeistungArgs(
    val configurationId: String,
    val displayContext: DisplayContext,
    val leistungId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext,
      args["leistungId"] as String
  )
}
data class MutationDeleteLeistungsPositionArgs(
    val configurationId: String,
    val displayContext: DisplayContext,
    val leistungId: String,
    val leistungsPositionId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext,
      args["leistungId"] as String,
      args["leistungsPositionId"] as String
  )
}
data class MutationImportProductDatabaseArgs(
    val versionName: String
) {
  constructor(args: Map<String, Any>) : this(
      args["versionName"] as String
  )
}
data class MutationSelectLeistungArgs(
    val configurationId: String,
    val displayContext: DisplayContext,
    val leistungId: String,
    val selection: Boolean
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext,
      args["leistungId"] as String,
      args["selection"] as Boolean
  )
}
data class MutationSelectLeistungsPositionArgs(
    val configurationId: String,
    val displayContext: DisplayContext,
    val leistungId: String,
    val leistungsPositionId: String,
    val selection: Boolean
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext,
      args["leistungId"] as String,
      args["leistungsPositionId"] as String,
      args["selection"] as Boolean
  )
}
data class Mutation(
    val archiveConfiguration: Boolean,
    val changeExecution: ConfigurationTable,
    val configureLeistungsPosition: ConfigurationTable,
    val copyConfiguration: String,
    val createConfiguration: String,
    val createLeistung: ConfigurationTable,
    val createLeistungsPosition: ConfigurationTable,
    val createProject: String,
    val deleteLeistung: ConfigurationTable,
    val deleteLeistungsPosition: ConfigurationTable,
    val importProductDatabase: String,
    val selectLeistung: ConfigurationTable,
    val selectLeistungsPosition: ConfigurationTable,
    val zzz: Boolean?
)

data class Project(
    val configurations: List<ConfigurationTable>,
    val id: String,
    val listingId: String
)

data class QueryConfigurationTableArgs(
    val configurationId: String,
    val displayContext: DisplayContext
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext
  )
}
data class QueryLeistungDetailsArgs(
    val configurationId: String,
    val displayContext: DisplayContext,
    val leistungId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String,
      args["displayContext"] as DisplayContext,
      args["leistungId"] as String
  )
}
data class QueryLeistungsPositionDefaultsArgs(
    val configurationId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["configurationId"] as String
  )
}
data class QueryMassedatenArgs(
    val projectId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["projectId"] as String
  )
}
data class QueryProjectArgs(
    val displayContext: DisplayContext,
    val id: String
) {
  constructor(args: Map<String, Any>) : this(
      args["displayContext"] as DisplayContext,
      args["id"] as String
  )
}
data class QueryProjectByListingIdArgs(
    val displayContext: DisplayContext,
    val listingId: String
) {
  constructor(args: Map<String, Any>) : this(
      args["displayContext"] as DisplayContext,
      args["listingId"] as String
  )
}
data class Query(
    val configurationTable: ConfigurationTable,
    val databaseVersions: List<DatabaseVersion>,
    val leistungDetails: LeistungDetails,
    val leistungsPositionDefaults: LeistungsPositionDefaults,
    val massedaten: List<Massedatum>?,
    val project: Project?,
    val projectByListingId: Project?,
    val zzz: Boolean?
)

enum class SchnellkonfigurationType(val label: String) {
      L("L"),
      M("M"),
      S("S");
        
  companion object {
    @JvmStatic
    fun valueOfLabel(label: String): SchnellkonfigurationType? {
      return values().find { it.label == label }
    }
  }
}

data class SideNavigation(
    val configurationTable: ConfigurationTable
)

data class Subscription(
    val zzz: Boolean?
)

data class UiElementTranslation(
    val languageCode: String,
    val translation: String
)

data class VergabeEinheit(
    val completenessState: CompletenessState,
    val displayName: List<UiElementTranslation>,
    val hasWarnings: Boolean,
    val id: String,
    val komponenten: List<VergabeEinheitKomponente>,
    val margin: Double,
    val moduleName: String,
    val netCosts: Double,
    val offerPriceGross: Double,
    val offerPriceNet: Double,
    val securitySurcharge: Double,
    val vatAmount: Double
)

data class VergabeEinheitKomponente(
    val completenessState: CompletenessState,
    val displayName: List<UiElementTranslation>,
    val hasWarnings: Boolean,
    val id: String,
    val leistung: List<Leistung>,
    val margin: Double,
    val netCosts: Double,
    val offerPriceGross: Double,
    val securitySurcharge: Double,
    val vatAmount: Double
)