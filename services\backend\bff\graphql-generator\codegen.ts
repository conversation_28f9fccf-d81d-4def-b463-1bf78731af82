import type {CodegenConfig} from '@graphql-codegen/cli';

const config: CodegenConfig = {
    overwrite: true,
    schema: [
        {
            '../../../../api/graphql/schema/**/*.graphqls': {
                commentDescriptions: true,
                assumeValidSDL: true,
            },
        },
    ],
    documents: '../../../../api/graphql/test/**/*.gql',
    ignoreNoDocuments: true,
    debug: true,
    generates: {
        //'../middleware-infra/target/generated-sources/graphql/src/main/kotlin/com/doorbit/bff/infra/api/GraphQLDTOs.kt': {
        '../src/main/kotlin/com/doorbit/projectconfigurator/bff/infra/api/dto/GraphQLDTOs.kt': {
            plugins: [
                'kotlin',
            ],
            config: {
                namingConvention: {
                    enumValues: 'keep',
                },
                package: 'com.doorbit.projectconfigurator.bff.infra.api.dto',
                withTypes: true,
                strictScalars: true,
                listType: 'List',
                scalars: {
                    Float: 'Double',
                }
            },
        },
    },
};

export default config;
