import Keycloak, { <PERSON><PERSON><PERSON>akError, KeycloakProfile } from 'keycloak-js';
import { computed, ref } from 'vue';
import { IS_DEVELOPMENT, IS_SAFARI } from '@/utility/environment';
import { Optional } from '@/model/Optional';
import { LSF__LANGUAGE_CODE } from '@/service/local-storage/local-storage';
import { useNetwork } from '@vueuse/core';

//Doku: https://www.keycloak.org/docs/latest/securing_apps/index.html#_javascript_adapter
class AuthService {
  // noinspection LocalVariableNamingConventionJS
  private static REDIRECT_URL = window.location.origin + '/account/';

  public readonly isInitialized = ref<boolean>(false);
  public readonly username = ref<Optional<string>>(null);
  public readonly userId = ref<Optional<string>>(null);
  public readonly isAdminUser = ref<boolean>(false);
  public readonly isPremiumUser = ref<boolean>(false);
  public readonly accessToken = ref<Optional<string>>(null);
  // noinspection LocalVariableNamingConventionJS
  private readonly _isLoggedIn = ref<boolean>(false);
  public readonly isLoggedIn = computed<boolean>(() => this.isInitialized.value && this._isLoggedIn.value);
  private readonly keycloak = new Keycloak({
    url: import.meta.env.VITE_KEYCLOAK_URL ?? 'https://auth-integ.doorbit.com/login/',
    realm: 'doorbit',
    clientId: 'doorbit-public'
  });

  constructor() {
    //you can't use function pointers and have to use closures here :( (no "this" context)
    this.keycloak.onReady = (authenticated) => this.onReady(authenticated ?? false);
    this.keycloak.onAuthSuccess = () => this.onAuthSuccess();
    this.keycloak.onAuthError = (error) => this.onAuthError(error);
    this.keycloak.onAuthRefreshSuccess = () => this.onAuthRefreshSuccess();
    this.keycloak.onAuthRefreshError = () => this.onAuthRefreshError();
    this.keycloak.onAuthLogout = () => this.onAuthLogout();
    this.keycloak.onTokenExpired = () => this.onTokenExpired();
  }

  public async triggerRegistration(redirectURL: string = AuthService.REDIRECT_URL): Promise<void> {
    if (IS_DEVELOPMENT) {
      console.log('Triggering registration with redirectURL', redirectURL, '…');
    }

    try {
      await this.keycloak.register({
        redirectUri: redirectURL,
        locale: LSF__LANGUAGE_CODE.state.value
      });
    } catch (e) {
      if (IS_DEVELOPMENT) {
        console.warn('Registration failed', e);
      }
    }
  }

  public async triggerLogin(redirectURL: string = AuthService.REDIRECT_URL): Promise<void> {
    if (IS_DEVELOPMENT) {
      console.log('Triggering login with redirectURL', redirectURL, '…');
    }

    try {
      await this.keycloak.login({
        redirectUri: redirectURL,
        locale: LSF__LANGUAGE_CODE.state.value
      });
    } catch (e) {
      if (IS_DEVELOPMENT) {
        console.warn('Login failed', e);
      }
    }
  }

  public async triggerPasswordChange(redirectURL: string = AuthService.REDIRECT_URL): Promise<void> {
    if (IS_DEVELOPMENT) {
      console.log('Triggering password change with redirectURL', redirectURL, '…');
    }

    try {
      await this.keycloak.login({
        redirectUri: redirectURL,
        locale: LSF__LANGUAGE_CODE.state.value,
        action: 'UPDATE_PASSWORD'
      });
    } catch (e) {
      if (IS_DEVELOPMENT) {
        console.warn('Password change failed', e);
      }
    }
  }

  public async triggerLogout(redirectURL: string = AuthService.REDIRECT_URL): Promise<void> {
    if (IS_DEVELOPMENT) {
      console.log('Triggering logout with redirectURL', redirectURL, '…');
    }

    try {
      await this.keycloak.logout({
        redirectUri: redirectURL
      });
    } catch (e) {
      if (IS_DEVELOPMENT) {
        console.warn('Logout failed', e);
      }
    }
  }

  public async openAccountManagement(): Promise<void> {
    if (IS_DEVELOPMENT) {
      console.log('Opening account management …');
    }

    await this.keycloak.accountManagement();
  }

  // public hasRealmRole(role: string): boolean {
  //     return this.keycloak.hasRealmRole(role)
  // }

  public async userProfile(): Promise<KeycloakProfile> {
    if (IS_DEVELOPMENT) {
      console.log('Fetching user profile …');
    }

    return await this.keycloak.loadUserProfile();
  }

  /**
   * Don't call this function outside of the file!
   */
  async initialize(idToken?: string, accessToken?: string, refreshToken?: string): Promise<void> {
    if (IS_DEVELOPMENT) {
      console.log('Initializing AuthService …');
      console.log('ID TOKEN', idToken);
      console.log('ACCESS TOKEN', accessToken);
      console.log('REFRESH TOKEN', refreshToken);
    }

    try {
      const isLoggedIn = await this.keycloak.init({
        useNonce: true,
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso.html',
        flow: 'standard',
        pkceMethod: 'S256',
        enableLogging: IS_DEVELOPMENT,
        messageReceiveTimeout: 10000,
        checkLoginIframe: idToken === undefined && accessToken === undefined && refreshToken === undefined,
        scope: 'openid', //space separated list of scopes
        idToken: idToken,
        token: accessToken,
        refreshToken: refreshToken
      });

      if (IS_DEVELOPMENT) {
        console.log(`Keycloak initialized (logged in: ${isLoggedIn})`);
      }
    } catch (e) {
      if (IS_DEVELOPMENT) {
        console.warn('Initialization failed', e);
      }

      if (IS_SAFARI) {
        //workaround for browsers that don't support silent check sso TODO: <<<< das muss im bff gelöst werden
        window.location.reload();
        return;
      }

      this.onReady(false);
    }
  }

  //Called when the access token is expired. If a refresh token is available the token can be refreshed with updateToken, or in cases where it is not (that is, with implicit flow) you can redirect to the login screen to obtain a new access token.
  private onTokenExpired() {
    if (IS_DEVELOPMENT) {
      console.log('Auth: Token expired');
      console.log('accessToken', this.accessToken.value);
      console.log('refreshToken', this.keycloak.refreshToken);
    }

    this.updateToken();
  }

  private updateToken() {
    this.keycloak
      .updateToken(30) //TODO muss noch in konstante / config
      .then((refreshed) => {
        if (refreshed) {
          if (IS_DEVELOPMENT) {
            console.log('Auth: Token refreshed (updateToken)');
          }
        } else {
          if (IS_DEVELOPMENT) {
            console.warn(
              'Auth: Token not refreshed, valid for ' +
                Math.round(this.keycloak.tokenParsed?.exp ?? 0 - Date.now() / 1000) +
                ' seconds'
            );
          }
        }
      })
      .catch((error) => {
        if (IS_DEVELOPMENT) {
          console.warn('Auth: Failed to refresh token (updateToken)', error);
        }

        //TODO: hier muss es iwi einen modal geben, der den user informiert, dass er sich neu anmelden muss

        /**
         * Nur token clearen, wenn man auch online war. Wenn das Internet weg war, sollte das Token am Leben bleiben,
         * damit die Session wiederhergestellt werden kann, wenn das Internet wieder da ist.
         */
        if (useNetwork().isOnline.value) {
          this.keycloak.clearToken();
        } else if (IS_DEVELOPMENT) {
          console.warn('Auth: Not clearing token, because the user is offline');
        }
      });
  }

  //Called if the user is logged out (will only be called if the session status iframe is enabled, or in Cordova mode).
  private onAuthLogout() {
    if (IS_DEVELOPMENT) {
      console.log('Auth: Logged out');
    }
    this._isLoggedIn.value = false;
    this.accessToken.value = null;
    this.userId.value = null;
    this.username.value = null;
  }

  //Called if there was an error while trying to refresh the token.
  private onAuthRefreshError() {
    if (IS_DEVELOPMENT) {
      console.warn('Auth: Failed to refresh token (onAuthRefreshError)');
    }
  }

  //Called when the token is refreshed.
  private onAuthRefreshSuccess() {
    if (IS_DEVELOPMENT) {
      console.log('Auth: Token refreshed (onAuthRefreshSuccess)');
    }
    this.refreshData();
  }

  //Called when the adapter is initialized.
  private onReady(isAuthenticated: boolean) {
    if (IS_DEVELOPMENT) {
      console.log(`Auth: Ready (authenticated: ${isAuthenticated})`);
    }
    this.isInitialized.value = true;
  }

  //Called when a user is successfully authenticated.
  private onAuthSuccess() {
    if (IS_DEVELOPMENT) {
      console.log('Auth: Success');
    }
    this.refreshData();
  }

  private refreshData() {
    this.accessToken.value = this.keycloak.token ?? null;
    this.userId.value = this.keycloak.subject ?? null;
    this.username.value = this.keycloak.idTokenParsed?.preferred_username ?? null;
    this.isAdminUser.value = this.keycloak.resourceAccess?.['doorbit-public']?.roles?.includes('admin') ?? false;
    this.isPremiumUser.value = this.keycloak.resourceAccess?.['doorbit-public']?.roles?.includes('premium') ?? false;
    this._isLoggedIn.value = true;

    // console.log("ID TOKEN", this.keycloak.idToken)
    // console.log("TOKEN", this.keycloak.token)
    // console.log("REFRESH TOKEN", this.keycloak.refreshToken)
  }

  //Called if there was an error during authentication.
  private onAuthError(error: KeycloakError) {
    if (IS_DEVELOPMENT) {
      console.warn('Auth: Error', error);
    }
  }
}

const authService = new AuthService();
authService.initialize().then();

/**
 * DON'T USE IT FOR ANY OTHER PURPOSE THAN THE SETUP
 */
export const AUTH_SERVICE = authService;
