<template>
  <d-generic-error v-if="!listingId" fullscreen :message="t('errorViews.missingListingId')">
    <span />
  </d-generic-error>
  <template v-else>
    <d-layout-desktop v-if="isProjectLoading" />
    <template v-else>
      <template v-if="!projectId">
        <d-apollo-error v-if="getProjectError" :error="getProjectError" :reload="restartGetProject" fullscreen />
        <d-generic-error v-else :message="t('errorViews.projectDataNotLoaded')" fullscreen />
      </template>
      <template v-else>
        <d-bar-and-content />
      </template>
    </template>
  </template>
</template>

<script setup lang="ts">
import { onMounted, provide, watch } from 'vue';
import DLayoutDesktop from '@/components/layout/d-layout-desktop.vue';
import DApolloError from '@/components/ui/d-apollo-error.vue';
import DGenericError from '@/components/ui/d-generic-error.vue';
import { initializeProjectService } from '@/service/use-project-service';
import { initializeConfigurationTableService } from '@/service/use-configuration-table-service';
import DBarAndContent from '@/components/content/d-bar-and-content.vue';
import { initializeLeistungspositionDefaultsService } from '@/service/use-leistungsposition-defaults-service';
import { useI18n } from 'vue-i18n';
import { initializeMassedatenService } from '@/service/use-massedaten-service';

const { t } = useI18n();
const { isProjectLoading, getProjectError, restartGetProject, projectId, listingId } =
  initializeProjectService(provide);
initializeConfigurationTableService(provide);
initializeLeistungspositionDefaultsService(provide);
const { getMassedatenVariables, loadMassedaten } = initializeMassedatenService(provide);

watch(projectId, (newVal) => {
  if (newVal) {
    getMassedatenVariables.value = { projectId: newVal };
    loadMassedaten();
  }
});

onMounted(() => {
  window.addEventListener('focus', () => loadMassedaten());
});
</script>

<style lang="scss" scoped></style>
