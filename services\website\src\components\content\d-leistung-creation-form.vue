<template>
  <v-card>
    <v-card-title class="text-pre-wrap">{{ t('domain.leistung.newLeistung') }}</v-card-title>
    <v-card-subtitle class="mx-2 text-pre-wrap">
      {{ [creationContext.vergabeEinheitName, creationContext.komponenteName].join(' > ') }}
    </v-card-subtitle>
    <d-apollo-error
      v-if="leistungUpdateError"
      :reload="createLeistung"
      :error="leistungUpdateError"
      :title="t('errorViews.serverErrorLeistungCreate')"
    />
    <v-form v-else v-model="valid" @submit.prevent="createLeistung">
      <v-card-text>
        <v-skeleton-loader v-if="isLeistungBeingCreated" type="card" />
        <template v-else>
          <v-text-field
            v-model="displayName"
            rounded
            density="compact"
            variant="outlined"
            :label="t('domain.displayName')"
            :rules="[formRules.required]"
            tabindex="0"
            autofocus
          />
          <v-select
            v-model="leistungType"
            :items="leistungsTypeItems"
            variant="outlined"
            rounded
            density="compact"
            class="mb-6 mt-4"
            hide-details
            :label="t('domain.leistung.leistungType')"
            tabindex="0"
          />
          <v-text-field
            v-model.number="offerPrice"
            type="number"
            min="0"
            :label="t('domain.leistung.offerPrice')"
            rounded
            variant="outlined"
            density="compact"
            tabindex="0"
          />
          <v-switch
            v-model="globallyAvailable"
            color="success"
            hide-details
            density="compact"
            :label="
              globallyAvailable
                ? t('domain.globallyAvailable.globallyAvailable')
                : t('domain.globallyAvailable.notGloballyAvailable')
            "
            tabindex="0"
          />
        </template>
      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="grey" :text="t('common.cancel')" tabindex="-1" @click="emits('finish')"></v-btn>
        <v-btn
          variant="tonal"
          type="submit"
          tabindex="0"
          :disabled="!valid || isLeistungBeingCreated"
          :text="t('common.save')"
        ></v-btn>
      </v-card-actions>
    </v-form>
  </v-card>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import { FormRules, formRulesKey } from '@/utility/form-rules';
import { LeistungCreationContext } from '@/components/content/LeistungCreationContext';
import { DConfigTableFragment, LeistungType, useDLeistungCreationMutation } from '@/adapter/graphql/generated/graphql';
import { useI18n } from 'vue-i18n';
import DApolloError from '@/components/ui/d-apollo-error.vue';
import { useProjectService } from '@/service/use-project-service';
import { useConfigurationTableService } from '@/service/use-configuration-table-service';

const { t } = useI18n();
const { selectedConfiguration } = useProjectService();
const { setConfigTable } = useConfigurationTableService();
const formRules = inject<FormRules>(formRulesKey)!;
const props = defineProps<{
  creationContext: LeistungCreationContext;
}>();
const emits = defineEmits<{
  finish: [];
}>();
const valid = ref(false);
const displayName = ref<string>('');
const globallyAvailable = ref<boolean>(false);
const leistungType = ref<LeistungType>('ZERO_VARIANT');
const offerPrice = ref<number>(0);

const leistungsTypeItems: { title: string; value: LeistungType }[] = [
  { title: t('domain.leistung.types.ZERO_VARIANT'), value: 'ZERO_VARIANT' },
  { title: t('domain.leistung.types.ALTERNATIVE'), value: 'ALTERNATIVE' },
  { title: t('domain.leistung.types.ADD_ON'), value: 'ADD_ON' }
];

const {
  mutate: createLeistungRequest,
  loading: isLeistungBeingCreated,
  error: leistungUpdateError
} = useDLeistungCreationMutation();

async function createLeistung() {
  const result = await createLeistungRequest({
    configurationId: selectedConfiguration.value!.configurationId,
    komponenteId: props.creationContext.komponenteId,
    payload: {
      displayName: displayName.value,
      globallyAvailable: globallyAvailable.value,
      leistungType: leistungType.value,
      offerPrice: offerPrice.value
    }
  });
  if (result?.data) {
    setConfigTable(result.data.createLeistung as DConfigTableFragment);
    emits('finish');
  } else {
    // need error? can result actually be null?
  }
}
</script>
