<template>
  <svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M63.1307 31.5301L46.1602 14.5595C43.817 12.2164 40.018 12.2164 37.6749 14.5595L20.7043 31.5301C18.3612 33.8732 18.3612 37.6722 20.7043 40.0154L37.6749 56.9859C40.018 59.3291 43.817 59.3291 46.1602 56.9859L63.1307 40.0154C65.4739 37.6722 65.4739 33.8732 63.1307 31.5301Z"
      :stroke="current.colors.primaryLight as string"
      stroke-width="3"
      stroke-miterlimit="10"
    />
    <path
      d="M51.2957 31.9578L34.3251 14.9873C31.982 12.6441 28.183 12.6441 25.8398 14.9873L8.86929 31.9578C6.52614 34.301 6.52614 38.1 8.86929 40.4431L25.8398 57.4137C28.183 59.7568 31.982 59.7568 34.3251 57.4137L51.2957 40.4431C53.6388 38.1 53.6388 34.301 51.2957 31.9578Z"
      :fill="current.colors.primary"
      :stroke="current.colors.primary"
      stroke-width="3"
      stroke-miterlimit="10"
    />
  </svg>
</template>
<script setup lang="ts">
import { useTheme } from 'vuetify';
const { current } = useTheme();
</script>
