# Über dieses Repo

Da dem Kunden Exklusivität vertraglich zugesichert ist, lagern wir die Codebasis für den Tab Project Konfigurator aus, damit keine Dependencies zu Code erzeugen, der uns gehört. Es ist wichtig zu verstehen, dass **nur** der Tab "Project Configurator" im Zielbild Exklusivität genießt, nicht aber die anderen Tabs im Flow. Insgesamt sind ca. 4 Tabs zu bauen, wovon ein Tab der eigentliche Project Configurator ist. Mit diesem fangen wir auch an am besten.

Ob die Exklusivitätskarte vom Kunden gezogen werden kann oder nicht, hängt zudem an Business Meilensteinen, die erreicht werden müssen. Werden diese nicht erreicht ist das Anrecht auf Exklusivität für immer erloschen. Daher brauchen wir auch keine Kunden-Namings "per se" im Code.

### Aufbau

In diesem Repo befindet sich Frontend und Backend gemeinsam, wie im portal-applications Repo auch.

Frontend: Vue3.js und Vuetify, GraphQL
Backend: Kotlin, Spring Boot, MongoDB

Architektur: Frontend <- GraphQL -> (1 Backend For Frontend -> Backends)

Der BFF und die Backends werden in dasselbe Kompilat deployed. Eine Ausleitung von mehreren dedizierten Deployments in der Cloud ist derzeit nicht erforderlich. Um dennoch gekapselt entwickeln zu können liegen die Backends als isolierte Maven Module vor.

## Dev setup

### MongoDb aufsetzen

```shell
cd devsetup
docker-compose -f docker-compose.yml up -d
```

### Bff Schemaerzeugung

```shell
cd services/backend/bff/graphql-generator
yarn install // Only on first use
yarn generate

cd ..
cd ..
mvn clean install
```

## Links

### Gemeinsame Projektsprache

https://airtable.com/appXUL0dUG6yNZw0w/shrT4tC6zviLECjT0/tblM46KNOsLbRpMTO

### Domänenwissen

https://www.bauprofessor.de/leistungsverzeichnis/

### Figma

* [Flow](https://www.figma.com/file/dukBAGA01jOWXf8JC42gnL/Mockups?type=design&node-id=36%3A105047&mode=design&t=KFeUe9ji92tGV0HS-1)
* [Project Configurator - Tab (Deep Dive)](https://www.figma.com/file/dukBAGA01jOWXf8JC42gnL/Mockups?type=design&node-id=25%3A105024&mode=design&t=KFeUe9ji92tGV0HS-1)
