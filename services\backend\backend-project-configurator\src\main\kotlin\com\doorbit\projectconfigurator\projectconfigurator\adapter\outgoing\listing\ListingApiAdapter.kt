package com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.listing

import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.WithLogger
import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.d
import com.doorbit.projectconfigurator.projectconfigurator.domain.massedaten.Massedatum
import org.springframework.stereotype.Component

@Component
class ListingApiAdapter(
    private val buildingApi: BuildingApi,
) {

    fun getMassedaten(listingId: String): Map<String, Massedatum>? {
        val start = System.currentTimeMillis()

        val result = buildingApi.getListingMassesById(listingId)
        LOGGER.d { "getMassedaten($listingId) from Listing Api took ${System.currentTimeMillis() - start}ms" }
        if (result.building == null) {
            LOGGER.d { "Listing $listingId has no building" }
            return null
        }

        return result.building.customData.data.mapNotNull {
            if (it.fieldValue == null) {
                null
            } else Massedatum(key = it.fieldName, value = it.fieldValue)
        }.associateBy(Massedatum::key)
    }

    companion object : WithLogger()

}