<template>
  <div class="sessionStatusWrapper pa-4">
    <div class="sessionStatus">
      <v-progress-circular v-if="isLoading" :size="mdAndUp ? 100 : 75" class="mb-10" indeterminate width="6" />
      <h2 v-if="mdAndUp">
        <slot />
      </h2>
      <h5 v-else>
        <slot />
      </h5>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useDisplay } from 'vuetify';

defineProps<{
  isLoading?: boolean;
}>();

const { mdAndUp } = useDisplay();
</script>

<style scoped>
.sessionStatusWrapper {
  display: flex;
  width: 100%;
  height: 100%;
  justify-items: center;
  align-items: center;
  flex-direction: row;
}

.sessionStatus {
  flex: 1 1 auto;
  text-align: center;
}
</style>
