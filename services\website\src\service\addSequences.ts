import {
  DConfigTableFragment,
  Leistung,
  LeistungsPosition,
  VergabeEinheit,
  VergabeEinheitKomponente
} from '@/adapter/graphql/generated/graphql';

function deepCopyTable(table: DConfigTableFragment): DConfigTableFragment {
  return {
    ...table,
    vergabeEinheiten: table.vergabeEinheiten.slice().map((vergabeEinheit) => ({
      ...vergabeEinheit,
      komponenten: vergabeEinheit.komponenten.slice().map((komponente) => ({
        ...komponente,
        leistung: komponente.leistung.slice().map((leistung) => ({
          ...leistung,
          leistungsPositionen: leistung.leistungsPositionen.slice()
        }))
      }))
    }))
  };
}

export function addSequences(table: DConfigTableFragment) {
  let currentModuleName: string | null = null;
  let currentModuleSeq = 0;
  let currentKomponenteSeq = 0;
  let currentLeistungSeq = 0;
  let currentLeistungsPositionSeq = 0;
  const tableCopy = deepCopyTable(table);
  tableCopy.vergabeEinheiten.forEach((ve, index) => {
    if (ve.moduleName !== currentModuleName) {
      currentModuleName = ve.moduleName;
      currentModuleSeq += 1;
    }
    (ve as VergabeEinheit & { moduleSeq?: number }).moduleSeq = currentModuleSeq;
    (ve as VergabeEinheit & { vergabeEinheitSeq?: number }).vergabeEinheitSeq = index;
    ve.komponenten.forEach((komponente) => {
      (komponente as VergabeEinheitKomponente & { komponenteSeq?: number }).komponenteSeq = currentKomponenteSeq;
      currentKomponenteSeq += 1;
      komponente.leistung.forEach((leistung) => {
        (leistung as Leistung & { leistungSeq?: number }).leistungSeq = currentLeistungSeq;
        currentLeistungSeq += 1;
        leistung.leistungsPositionen.forEach((lp) => {
          (lp as LeistungsPosition & { leistungsPositionSeq?: number }).leistungsPositionSeq =
            currentLeistungsPositionSeq;
          currentLeistungsPositionSeq += 1;
        });
      });
    });
  });
  return tableCopy;
}
