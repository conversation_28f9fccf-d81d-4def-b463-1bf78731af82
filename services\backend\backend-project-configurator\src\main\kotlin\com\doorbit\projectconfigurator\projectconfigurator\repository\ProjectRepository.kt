package com.doorbit.projectconfigurator.projectconfigurator.repository

import com.doorbit.projectconfigurator.projectconfigurator.domain.ProjectConfigurationId
import com.doorbit.projectconfigurator.projectconfigurator.domain.project.Project
import org.bson.types.ObjectId
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.core.env.Environment
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.index.Index
import org.springframework.data.mongodb.core.query.Criteria.where
import org.springframework.data.mongodb.core.query.Query.query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.mongodb.core.query.isEqualTo
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Repository
import org.springframework.web.server.ResponseStatusException

@Repository
class ProjectRepository(
    private val mongoTemplate: MongoTemplate,
    environment: Environment
) : RepositoryBase(Project::class.java.simpleName, environment) {

    fun createProject(listingId: String): Project {
        findOrCreateProjectByListingId(listingId)?.let {
            throw ResponseStatusException(HttpStatus.CONFLICT, "Project with listingId $listingId already exists")
        }

        return mongoTemplate.save(Project(listingId = listingId, configurationIds = emptyList()))
    }

    fun findOrCreateProjectByListingId(listingId: String, createIfMissing: Boolean = false): Project? {
        val result = mongoTemplate.findOne(query(where("listingId").isEqualTo(listingId)), Project::class.java)
        if (result == null && createIfMissing) {
            return createProject(listingId)
        }

        return result
    }

    fun findById(id: ObjectId): Project? {
        return mongoTemplate.findById(id, Project::class.java)
    }

    fun addConfigurationId(projectId: String, configurationId: ProjectConfigurationId) {
        val idQuery = query(where("_id").isEqualTo(projectId))
        val update = Update().push("configurationIds", configurationId)
        val result = mongoTemplate.updateFirst(idQuery, update, Project::class.java)
        if (result.matchedCount == 0L) {
            // TODO
            // throw ResponseStatusException(HttpStatus.NOT_FOUND, "Project with id $projectId not found")
        }
    }

    @EventListener(ApplicationReadyEvent::class)
    fun init() {
        val indexOps = mongoTemplate.indexOps(Project::class.java)
        indexOps.ensureIndex(Index().named("listingId").on("listingId", Sort.Direction.ASC))
    }

}