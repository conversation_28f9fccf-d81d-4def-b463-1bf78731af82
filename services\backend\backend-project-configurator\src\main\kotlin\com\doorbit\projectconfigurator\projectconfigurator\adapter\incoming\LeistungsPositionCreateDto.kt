package com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming

import java.math.BigDecimal

data class LeistungsPositionCreateDto(
    val displayName: String,
    val unit: String,
    val unitPrice: BigDecimal,
    val vatRatePercent: BigDecimal,
    val marginPercent: BigDecimal,
    val securitySurchargePercent: BigDecimal,
    val amount: BigDecimal,
    val saveAsGlobal: Boolean,
    val notes: String?
)