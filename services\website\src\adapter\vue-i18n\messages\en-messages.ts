import { DefineLocaleMessage } from 'vue-i18n';

const enMessages: DefineLocaleMessage = {
  $vuetify: {
    input: {
      clear: 'Clear'
    },
    carousel: {
      prev: 'Previous',
      next: 'Next',
      ariaLabel: {
        delimiter: '-'
      }
    },
    open: 'Open',
    close: 'Close',
    badge: 'We dont need no stinkin badges!',
    dataFooter: {
      pageText: 'Page',
      itemsPerPageText: 'Items per page'
    },
    noDataText: 'No Data'
  },
  errorViews: {
    notFound: '404 - Page not found',
    genericError: 'Oops, something unexpected happened ...',
    apolloError: 'Server Error',
    reload: 'Reload',
    consultAdmin: 'Please contact the administrator',
    missingListingId: 'Missing Listing-ID in URL',
    projectDataNotLoaded: 'Project data could not be loaded',
    serverErrorLeistungCreate: 'Server-error, could not create new service.',
    noMassedaten: 'No Bill of Quantities available for Listing-ID'
  },
  domain: {
    massedaten: 'Bill of Quantities',
    preSelection: 'Quick Configuration',
    notes: 'Notes',
    vergabeEinheitenFilters: {
      searchLabel: 'Search',
      filterLabel: 'Filter',
      filterItems: {
        selected: 'Selected',
        invalid: 'Invalid',
        incomplete: 'Incomplete',
        configured: 'Configured',
        partiallyConfigured: 'Partially Configured',
        notConfigured: 'Not Configured'
      }
    },
    costOverview: 'Cost Overview',
    globallyAvailable: {
      globallyAvailable: 'Available across configurations',
      notGloballyAvailable: 'Available only in current configuration'
    },
    displayName: 'Display Name',
    amount: 'Amount',
    priceGross: 'Price (Gross)',
    unitPrice: 'Unit Price',
    securitySurcharge: 'Security Surcharge',
    margin: 'Margin',
    offerPriceNet: 'Offer Price (Net)',
    vatAmount: 'Vat Amount',
    vatRate: 'Vat Rate',
    netCosts: 'Project costs',
    totalProjectCosts: 'Total Project Costs',
    offerPriceGross: 'Offer Price (Gross)',
    calculationFormula: 'Calculation Formula',
    contractorName: 'Renaldo',
    dataSource: {
      dataSource: 'Data Source',
      created: 'Created',
      imported: 'Imported/AirTable'
    },
    selfService: 'Self-service',
    eligible: 'Eligible for Funding',
    notEligible: 'Not Eligible for Funding',
    dataSheet: 'Data Sheet',
    execution: 'Execution',
    vergabeEinheit: {
      component: 'Component',
      grossQuotePrice: 'Gross Quote Price',
      netPlanningCosts: 'Planning Costs (Net)'
    },
    leistung: {
      selected: 'Service Selected',
      notSelected: 'Service Not Selected',
      newLeistung: 'Create new Service',
      leistung: 'Service',
      uValue: 'U-Unit',
      manufacturer: 'Manufacturer',
      offerPriceGross: 'Offer Price (Gross)',
      offerPrice: 'Offer Price',
      leistungType: 'Service Type',
      unit: 'Unit',
      types: {
        ZERO_VARIANT: 'Zero-Variant',
        ALTERNATIVE: 'Alternative',
        ADD_ON: 'Add-On'
      }
    },
    leistungsposition: {
      takeFromMassedaten: 'Click to set amount',
      creationInstruction: 'All attributes are optional. Defaults will be used for fields left blank.',
      unitCreateOrSelect: 'Unit (create or select)',
      leistungspositionen: 'Articles',
      newLeistungsposition: 'New Article',
      description: 'Description',
      unitPrice: 'Unit Price',
      dinNumber: 'DIN Number'
    },
    configurations: {
      archiveConfirm: 'Do you really want to delete the configuration?',
      createNewConfig: 'Create new configuration',
      createNewError: 'Could not create configuration',
      databaseVersion: 'Database Version',
      newDatabaseVersion: 'Create new Database Version',
      configLabel: 'Label',
      noneCreated: 'No Configurations created yet',
      createFirst: 'Create your first configuration by clicking + above',
      noDbVersions: 'No DB versions available'
    }
  },
  common: {
    ok: 'OK',
    yes: 'Yes',
    no: 'No',
    new: 'New',
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    prev: 'Previous',
    next: 'Next',
    download: 'Download',
    redirectMessage: 'You will be redirected shortly …',
    pleaseWait: 'Please Wait',
    back: 'Back'
  },
  formRules: {
    required: 'Required value'
  }
};

export default enMessages;
