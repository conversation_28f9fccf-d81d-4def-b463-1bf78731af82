fragment DConfigTable on ConfigurationTable {
  calculationResult {
    offerPriceGross
    offerPriceNet
    vatAmount
    calculationsByModule {
      moduleName
      offerPriceGross
    }
  }
  vergabeEinheiten {
    id
    moduleName
    displayName {
      languageCode
      translation
    }
    completenessState
    hasWarnings
    netCosts
    offerPriceNet
    offerPriceGross
    margin
    vatAmount
    securitySurcharge
    komponenten {
      id
      displayName {
        languageCode
        translation
      }
      completenessState
      netCosts
      offerPriceGross
      margin
      vatAmount
      securitySurcharge
      hasWarnings
      leistung {
        id
        isSelected
        type
        cells {
          key
          intValue
          doubleValue
          stringValue
          booleanValue
        }
        leistungsPositionen {
          id
          cells {
            key
            intValue
            doubleValue
            stringValue
            booleanValue
          }
        }
      }
    }
  }
}
