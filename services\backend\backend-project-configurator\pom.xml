<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>backend-project-configurator</artifactId>

    <parent>
        <groupId>com.doorbit.projectconfigurator</groupId>
        <artifactId>backend-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <kotlin.version>1.9.23</kotlin.version>
        <findbugs.version>3.0.2</findbugs.version>
        <java.version>21</java.version>
        <compiler-plugin.version>3.13.0</compiler-plugin.version>
        <openapi.version>7.4.0</openapi.version>
        <slack.version>1.39.2</slack.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>1.9.23</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>${findbugs.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>0.2.6</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>com.slack.api</groupId>
            <artifactId>slack-api-client</artifactId>
            <version>${slack.version}</version>
        </dependency>

        <!-- Test stuff -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit5</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- OpenAPI generator -->
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi.version}</version>

                <executions>
                    <execution>
                        <id>listing-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generatorName>kotlin</generatorName>
                            <library>jvm-spring-restclient</library>
                            <inputSpec>${project.basedir}/../../../api/rest/listing-api.yaml</inputSpec>
                            <apiPackage>com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.listing</apiPackage>
                            <modelPackage>com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.listing.dto</modelPackage>
                            <invokerPackage>com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.listing</invokerPackage>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <modelNameSuffix>Dto</modelNameSuffix>

                            <generateApis>true</generateApis>
                            <generateModels>true</generateModels>

                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>

                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>

                            <generateSupportingFiles>true</generateSupportingFiles>

                            <configOptions>
                                <annotationLibrary>none</annotationLibrary>
                                <asyncNative>true</asyncNative>
                                <serializationLibrary>jackson</serializationLibrary>
                                <useGzipFeature>true</useGzipFeature>
                                <openApiNullable>false</openApiNullable>
                                <useSpringBoot3>true</useSpringBoot3>
                            </configOptions>
                            <importMappings>DateTime=java.time.Instant</importMappings>
                            <typeMappings>DateTime=java.time.Instant</typeMappings>
                        </configuration>
                    </execution>

                    <execution>
                        <id>airtable-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generatorName>kotlin</generatorName>
                            <library>jvm-spring-restclient</library>
                            <inputSpec>${project.basedir}/../../../api/rest/airtable-product-database-api.yaml</inputSpec>
                            <apiPackage>com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.airtable</apiPackage>
                            <modelPackage>com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.airtable.dto</modelPackage>
                            <invokerPackage>com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.airtable</invokerPackage>
                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                            <modelNameSuffix>Dto</modelNameSuffix>

                            <generateApis>true</generateApis>
                            <generateModels>true</generateModels>

                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>

                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateModelDocumentation>false</generateModelDocumentation>

                            <generateSupportingFiles>true</generateSupportingFiles>

                            <configOptions>
                                <annotationLibrary>none</annotationLibrary>
                                <asyncNative>true</asyncNative>
                                <serializationLibrary>jackson</serializationLibrary>
                                <useGzipFeature>true</useGzipFeature>
                                <openApiNullable>false</openApiNullable>
                                <useSpringBoot3>true</useSpringBoot3>
                            </configOptions>
                            <importMappings>DateTime=java.time.Instant</importMappings>
                            <typeMappings>DateTime=java.time.Instant</typeMappings>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>