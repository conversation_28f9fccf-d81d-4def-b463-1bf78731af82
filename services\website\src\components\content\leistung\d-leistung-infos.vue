<template>
  <div>
    <v-row>
      <v-col cols="12" md="6">
        <v-carousel v-if="leistungDetailsData.leistungDetails.imageUrls.length" height="290" :show-arrows="false">
          <v-carousel-item
            v-for="imageUrl of leistungDetailsData.leistungDetails.imageUrls"
            :key="imageUrl"
            :src="imageUrl"
          ></v-carousel-item>
        </v-carousel>
      </v-col>
      <v-col cols="12" md="6" order-md="first">
        <!--          <div class="mt-2 mb-6 ml-6" v-html="formattedText" />-->
        <v-table density="compact" class="d-leistung__details-table">
          <tbody>
            <tr v-if="typeof leistungDetailsData.leistungDetails.table.uValue === 'number'">
              <td>{{ t('domain.leistung.uValue') }}</td>
              <td>{{ leistungDetailsData.leistungDetails.table.uValue }}</td>
            </tr>
            <tr v-if="leistungDetailsData.leistungDetails.table.manufacturer">
              <td>{{ t('domain.leistung.manufacturer') }}</td>
              <td>{{ leistungDetailsData.leistungDetails.table.manufacturer }}</td>
            </tr>
            <tr v-if="typeof leistungDetailsData.leistungDetails.table.offerPriceGross === 'number'">
              <td>{{ t('domain.leistung.offerPriceGross') }}</td>
              <td>{{ currencyFormat(leistungDetailsData.leistungDetails.table.offerPriceGross, locale) }}</td>
            </tr>
            <tr
              v-if="
                typeof leistungDetailsData.leistungDetails.isCustomCreated === 'boolean' &&
                displayContext === 'BACKOFFICE'
              "
            >
              <td>{{ t('domain.dataSource.dataSource') }}</td>
              <td>
                {{
                  leistungDetailsData.leistungDetails.isCustomCreated
                    ? t('domain.dataSource.created')
                    : t('domain.dataSource.imported')
                }}
              </td>
            </tr>
          </tbody>
        </v-table>
        <div v-if="displayContext === 'BACKOFFICE'" class="d-flex align-center mt-6">
          <v-card-title>Leistungspositionen</v-card-title>
          <v-spacer />
          <d-leistingsposition-creation-form @submit="onLeistungspositionCreate" />
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts" setup>
import {
  ConfigurationTable,
  DGetLeistungDetailsQuery,
  useDLeistungspositionCreationMutation
} from '@/adapter/graphql/generated/graphql';
import { LeistungspositionCreationForm } from '@/components/content/leistung/LeistungspositionCreationForm';
import DLeistingspositionCreationForm from '@/components/content/leistung/d-leistingsposition-creation-form.vue';
import { useProjectService } from '@/service/use-project-service';
import { currencyFormat } from '@/utility/filter';
import { useI18n } from 'vue-i18n';

const { locale, t } = useI18n();
const { selectedConfiguration, displayContext } = useProjectService();
const { mutate: createLeistungsposition } = useDLeistungspositionCreationMutation();

const props = defineProps<{
  leistungDetailsData: DGetLeistungDetailsQuery;
}>();
const emits = defineEmits<{ newTableData: [data?: ConfigurationTable] }>();

async function onLeistungspositionCreate({
  displayName,
  globallyAvailable,
  marginPercent,
  securitySurchargePercent,
  unitPrice,
  vatRatePercent,
  amount,
  unit,
  notes
}: LeistungspositionCreationForm) {
  const result = await createLeistungsposition({
    configurationId: selectedConfiguration.value!.configurationId,
    leistungId: props.leistungDetailsData.leistungDetails.id,
    payload: {
      displayName,
      globallyAvailable,
      marginPercent,
      securitySurchargePercent,
      unitPrice,
      vatRatePercent,
      amount,
      unit,
      notes
    }
  });
  emits('newTableData', result?.data?.createLeistungsPosition as ConfigurationTable | undefined);
}
</script>

<style scoped>
.d-leistung__details-table tr:nth-of-type(odd) {
  background-color: rgb(241, 243, 244);
}
</style>
