<template>
  <v-toolbar density="comfortable">
    <v-toolbar-title>{{ t('domain.configurations.databaseVersion') }}</v-toolbar-title>
    <v-toolbar-items>
      <v-dialog max-width="300">
        <template #activator="{ props: activatorProps }">
          <v-btn flat color="primary" variant="text" :prepend-icon="mdiPlus" v-bind="activatorProps">{{
            t('common.new')
          }}</v-btn>
        </template>
        <template #default="{ isActive }">
          <v-card width="300">
            <v-card-title class="text-pre-wrap">{{ t('domain.configurations.newDatabaseVersion') }}</v-card-title>
            <v-form v-model="valid" @submit.prevent="onSubmit">
              <v-card-text>
                <v-text-field
                  v-model.trim="displayName"
                  rounded
                  density="compact"
                  variant="outlined"
                  label="Version Name"
                  :rules="[formRules.required]"
                  autofocus
                  tabindex="0"
                />
              </v-card-text>
              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="grey" :text="t('common.cancel')" tabindex="-1" @click="isActive.value = false"></v-btn>
                <v-btn
                  variant="tonal"
                  type="submit"
                  :disabled="!valid"
                  :text="t('common.save')"
                  tabindex="0"
                  @click="isActive.value = false"
                ></v-btn>
              </v-card-actions>
            </v-form>
          </v-card>
        </template>
      </v-dialog>
    </v-toolbar-items>
  </v-toolbar>
  <v-container class="d-flex justify-center">
    <v-card width="300">
      <div v-if="loading || creating">
        <v-skeleton-loader width="300" type="card" />
      </div>
      <v-list v-else-if="result?.databaseVersions">
        <v-list-item
          v-for="databseVersion in result?.databaseVersions"
          :key="databseVersion.databaseId"
          :value="databseVersion.databaseId"
          :ripple="false"
          class="cursor-text"
        >
          <v-list-item-title>{{ databseVersion.version }}</v-list-item-title>
          <v-list-item-subtitle>{{ databseVersion.databaseId }}</v-list-item-subtitle>
          <v-list-item-subtitle class="font-weight-bold"
            >{{ new Date(databseVersion.createdAt).toLocaleDateString() }}
            {{ new Date(databseVersion.createdAt).toLocaleTimeString() }}</v-list-item-subtitle
          >
        </v-list-item>
      </v-list>
    </v-card>
  </v-container>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useDCreateDbVersionMutation, useDGetDatabaseVersionsQuery } from '@/adapter/graphql/generated/graphql';
import { mdiPlus } from '@mdi/js';
import { formRules } from '@/utility/form-rules';
import { ref } from 'vue';

const { t } = useI18n();
const { result, loading, refetch } = useDGetDatabaseVersionsQuery();
const { loading: creating, mutate } = useDCreateDbVersionMutation();

const valid = ref(false);
const displayName = ref<string>('');

async function onSubmit() {
  const res = await mutate({ versionName: displayName.value });
  res?.data?.importProductDatabase && refetch();
}
</script>

<style lang="scss" scoped></style>
