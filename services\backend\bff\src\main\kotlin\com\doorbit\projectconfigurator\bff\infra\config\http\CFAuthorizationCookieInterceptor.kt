package com.doorbit.projectconfigurator.bff.infra.config.http

import com.doorbit.projectconfigurator.bff.infra.config.CloudflareCookieValidator
import org.springframework.context.annotation.Profile
import org.springframework.http.HttpHeaders.COOKIE
import org.springframework.http.HttpRequest
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import org.springframework.stereotype.Component

@Component
@Profile("local-to-integ")
class CFAuthorizationCookieInterceptor(
    private val cookieValidator: CloudflareCookieValidator,
) : ClientHttpRequestInterceptor {

    override fun intercept(request: HttpRequest, body: ByteArray, execution: ClientHttpRequestExecution): ClientHttpResponse {
        request.headers.add(<PERSON><PERSON>IE, cookieValidator.cookie())

        return execution.execute(request, body)
    }
}
