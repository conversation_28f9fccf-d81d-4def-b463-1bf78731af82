<template>
  <div :class="fullscreen ? 'd-flex flex-column justify-center align-center ma-4' : ''">
    <v-card class="pa-4">
      <v-card-title class="text-pre-wrap">{{ title ?? t('errorViews.apolloError') }}</v-card-title>
      <v-card-subtitle class="text-pre-wrap">{{ error.name }}: {{ error.message }}</v-card-subtitle>
      <v-card-text>{{ error.graphQLErrors }}</v-card-text>
      <v-card-actions class="justify-end">
        <v-btn color="black" @click="push('/')">{{ t('common.back') }}</v-btn>
        <v-btn @click="reload">{{ t('errorViews.reload') }}</v-btn>
      </v-card-actions>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ApolloError } from '@apollo/client';
import { useRouter } from 'vue-router';

const { t } = useI18n();
const { push } = useRouter();
defineProps<{ title?: string; error: ApolloError; reload: () => void; fullscreen?: boolean }>();
</script>
