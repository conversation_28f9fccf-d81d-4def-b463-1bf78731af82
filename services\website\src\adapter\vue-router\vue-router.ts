import { createRouter, createWebHistory } from 'vue-router';
import { useAuth } from '@/service/auth/use-auth';
import { watch } from 'vue';
import { IS_DEVELOPMENT } from '@/utility/environment';
import { UNHANDLED_ERROR } from '@/utility/error-handler';

const router = createRouter({
  history: createWebHistory(),
  routes: [],
  strict: true,
  scrollBehavior() {
    //scroll to top on route change
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          left: 0,
          top: 0
        });
      }, 150 / 2);
    });
  }
});

router.onError((error) => {
  console.log('Router found unhandled error. Forwarding ...', error);

  UNHANDLED_ERROR.value = error;
});

const ROUTE_NAME_LOGIN = 'login';
const ROUTE_NAME_LOGOUT = 'logout';

//best way to debug this: browser JS console with "Preserve log" enabled (and comment in the console.log()s)
router.beforeEach(async (to) => {
  const needsInitializedAuthService = to.name === ROUTE_NAME_LOGIN || to.name === ROUTE_NAME_LOGOUT;

  if (!needsInitializedAuthService && (to.meta.needsAuth == null || !to.meta.needsAuth)) {
    return true;
  }

  const { isAuthInitialized, isLoggedIn } = useAuth();

  if (!isAuthInitialized.value) {
    await new Promise<void>((resolve) => {
      watch(
        isAuthInitialized,
        (isInitialized) => {
          if (isInitialized) {
            resolve();
          } else if (IS_DEVELOPMENT) {
            console.log('AuthService not ready yet, waiting for initialization …');
          }
        },
        {
          immediate: true
        }
      );
    });
  }

  if (needsInitializedAuthService) {
    return true;
  }

  if (isLoggedIn.value) {
    return true;
  }

  return {
    name: ROUTE_NAME_LOGIN,
    query: {
      redirectURL: window.location.origin + to.fullPath
    }
  };
});

router.addRoute({
  path: '/',
  name: 'root',
  redirect: {
    name: 'configurator'
  }
});
router.addRoute({
  path: '/login',
  name: ROUTE_NAME_LOGIN,
  component: () => import('@/components/auth/d-login.vue'),
  props: (route) => ({
    redirectURL: route.query.redirectURL
  })
});
router.addRoute({
  path: '/logout',
  name: ROUTE_NAME_LOGOUT,
  component: () => import('@/components/auth/d-logout.vue'),
  props: (route) => ({
    redirectURL: route.query.redirectURL
  })
});
router.addRoute({
  path: '/configurator',
  name: 'configurator',
  component: () => import('@/components/content/d-configurator.vue'),
  children: [],
  meta: {
    needsAuth: false
  }
});
router.addRoute({
  path: '/db',
  name: 'db',
  component: () => import('@/components/content/d-db.vue'),
  children: [],
  meta: {
    needsAuth: false
  }
});
router.addRoute({
  path: '/:pathMatch(.*)*',
  name: 'notFound',
  component: () => import('@/components/content/d-not-found.vue')
});
//ORDER MATTERS <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<

export default router;
