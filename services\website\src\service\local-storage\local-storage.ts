// EXTEND THIS LIST AS YOU WISH >>>>>>>>>>>>>>>>>>>
import { LocalStorageField } from '@/service/local-storage/LocalStorageField';
import { IS_DEVELOPMENT } from '@/utility/environment';
import { LanguageCode, LanguageCodeValues } from '@/adapter/vue-i18n/LanguageCode';
import { App, inject, InjectionKey, Ref } from 'vue';
import { usePreferredLanguages } from '@vueuse/core';

const preferredLanguages = usePreferredLanguages().value; //example: 'en-US', 'de-DE', 'de', 'en'
const defaultLanguageCode =
  preferredLanguages
    .map((language) => language.split('-')[0]) //take only the language (first part)
    .map((language) => language as LanguageCode)
    .find((language) => LanguageCodeValues.includes(language)) ?? 'de';
if (IS_DEVELOPMENT) {
  console.log('preferredLanguages', preferredLanguages);
  console.log('defaultLanguageCode', defaultLanguageCode);
}
export const LSF__LANGUAGE_CODE = new LocalStorageField<LanguageCode>('languageCode', defaultLanguageCode);

export type LocalStorage = {
  languageCode: Ref<LanguageCode>;
};

const LocalStorageInjection = Symbol('localStorage') as InjectionKey<LocalStorage>;

export function initializeLocalStorage(app: App<Element>) {
  app.provide<LocalStorage>(LocalStorageInjection, {
    languageCode: LSF__LANGUAGE_CODE.state
  });
}

export function useLocalStorage(): LocalStorage {
  return inject<LocalStorage>(LocalStorageInjection)!;
}
