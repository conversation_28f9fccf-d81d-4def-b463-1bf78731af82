package com.doorbit.projectconfigurator.bff.infra.api.dto

import com.doorbit.projectconfigurator.bff.core.domain.extension.rounded

object MassedatenMapper {

    fun toDto(massedaten: Map<String, com.doorbit.projectconfigurator.projectconfigurator.domain.massedaten.Massedatum>): List<Massedatum> {
        return listOf(
            Massedatum(
                key = "dimensions",
                valueDouble = null,
                displayName = toTranslation(de = "Grundriss (L x B)", en = "Dimensions (L x W)"),
                unitDisplayName = "",
                icon = "mdiRuler",
                valueInt = null,
                valueString = massedaten["dimensions"]?.value as? String  ?: "",
            ),
            Massedatum(
                key = "netto_raumflaeche",
                valueDouble = (massedaten["netto_raumflaeche"]?.value as? Double)?.rounded(2),
                displayName = toTranslation(de = "Bruttogrundfläche (BGF)", en = "Bruttogrundfläche (BGF)"),
                unitDisplayName = "m²",
                icon = "mdiFloorPlan",
                valueInt = null,
                valueString = null
            ),
            Massedatum(
                key = "netto_rauminhalt",
                valueDouble = (massedaten["netto_rauminhalt"]?.value as? Double)?.rounded(2) ?: 0.0,
                displayName = toTranslation(de = "Brutto-Rauminhalt (BRI)", en = "Brutto-Rauminhalt (BRI)"),
                unitDisplayName = "m³",
                icon = "mdiCube",
                valueInt = null,
                valueString = null
            ),
            Massedatum(
                key = "storey_count",
                valueDouble = null,
                displayName = toTranslation(de = "Geschosse", en = "Storeys"),
                unitDisplayName = "Stk.",
                icon = "mdiFloorPlan",
                valueInt = massedaten["storey_count"]?.value as? Int ?: 0,
                valueString = null
            ),
            Massedatum(
                key = "full_storey_count",
                valueDouble = null,
                displayName = toTranslation(de = "Vollgeschosse", en = "Full storeys"),
                unitDisplayName = "Stk.",
                icon = "mdiFloorPlan",
                valueInt = massedaten["full_storey_count"]?.value as? Int ?: 0,
                valueString = null
            ),
            Massedatum(
                key = "balcony_count",
                valueDouble = null,
                displayName = toTranslation(de = "Balkone", en = "Balconies"),
                unitDisplayName = "Stk.",
                icon = "mdiBalcony",
                valueInt = massedaten["balcony_count"]?.value as? Int ?: 0,
                valueString = null
            ),
            Massedatum(
                key = "inside_area_net",
                valueDouble = (massedaten["inside_area_net"]?.value as? Double)?.rounded(2) ?: 0.0,
                displayName = toTranslation(de = "Wandfläche (innen, netto)", en = "Wall area (inside, net)"),
                unitDisplayName = "m²",
                icon = "mdiTextureBox",
                valueInt = null,
                valueString = null
            ),
            Massedatum(
                key = "inside_area_gross",
                valueDouble = (massedaten["inside_area_gross"]?.value as? Double)?.rounded(2) ?: 0.0,
                displayName = toTranslation(de = "Wandfläche (innen, brutto)", en = "Wall area (inside, gross)"),
                unitDisplayName = "m²",
                icon = "mdiTextureBox",
                valueInt = null,
                valueString = null
            ),
            Massedatum(
                key = "outside_area_net",
                valueDouble = (massedaten["outside_area_net"]?.value as? Double)?.rounded(2) ?: 0.0,
                displayName = toTranslation(de = "Wandfläche (außen, netto)", en = "Wall area (outside, net)"),
                unitDisplayName = "m²",
                icon = "mdiTextureBox",
                valueInt = null,
                valueString = null
            ),
            Massedatum(
                key = "outside_area_gross",
                valueDouble = (massedaten["outside_area_gross"]?.value as? Double)?.rounded(2) ?: 0.0,
                displayName = toTranslation(de = "Wandfläche (außen, brutto)", en = "Wall area (outside, gross)"),
                unitDisplayName = "m²",
                icon = "mdiTextureBox",
                valueInt = null,
                valueString = null
            ),
            Massedatum(
                key = "window_count",
                valueDouble = null,
                displayName = toTranslation(de = "Anzahl der Fenster", en = "Number of windows"),
                unitDisplayName = "Stk.",
                icon = "mdiWindowClosedVariant",
                valueInt = massedaten["window_count"]?.value as? Int ?: 0,
                valueString = null
            ),
            Massedatum(
                key = "outside_window_count",
                valueDouble = null,
                displayName = toTranslation(de = "Fenster in Außenwänden", en = "Windows in outside walls"),
                unitDisplayName = "Stk.",
                icon = "mdiWindowClosedVariant",
                valueInt = massedaten["outside_window_count"]?.value as? Int ?: 0,
                valueString = null
            ),
            Massedatum(
                key = "window_area",
                valueDouble = (massedaten["window_area"]?.value as? Double)?.rounded(2) ?: 0.0,
                displayName = toTranslation(de = "Fensterfläche", en = "Window area"),
                unitDisplayName = "m²",
                icon = "mdiWindowClosedVariant",
                valueInt = null,
                valueString = null
            ),
            Massedatum(
                key = "outside_window_area",
                valueDouble = (massedaten["outside_window_area"]?.value as? Double)?.rounded(2) ?: 0.0,
                displayName = toTranslation(de = "Fensterfläche in Außenwänden", en = "Window area in outside walls"),
                unitDisplayName = "m²",
                icon = "mdiWindowClosedVariant",
                valueInt = null,
                valueString = null
            ),
            Massedatum(
                key = "door_count",
                valueDouble = null,
                displayName = toTranslation(de = "Anzahl der Türen", en = "Number of doors"),
                unitDisplayName = "Stk.",
                icon = "mdiDoor",
                valueInt = massedaten["door_count"]?.value as? Int ?: 0,
                valueString = null
            ),
            Massedatum(
                key = "outside_door_count",
                valueDouble = null,
                displayName = toTranslation(de = "Türen in Außenwänden", en = "Doors in outside walls"),
                unitDisplayName = "Stk.",
                icon = "mdiDoor",
                valueInt = massedaten["outside_door_count"]?.value as? Int ?: 0,
                valueString = null
            ),
            Massedatum(
                key = "door_area",
                valueDouble = (massedaten["door_area"]?.value as? Double)?.rounded(2) ?: 0.0,
                displayName = toTranslation(de = "Türfläche", en = "Door area"),
                unitDisplayName = "m²",
                icon = "mdiDoor",
                valueInt = null,
                valueString = null
            ),
            Massedatum(
                key = "outside_door_area",
                valueDouble = (massedaten["outside_door_area"]?.value as? Double)?.rounded(2) ?: 0.0,
                displayName = toTranslation(de = "Türfläche in Außenwänden", en = "Door area in outside walls"),
                unitDisplayName = "m²",
                icon = "mdiDoor",
                valueInt = null,
                valueString = null
            )
        )
    }

    private fun toTranslation(de: String, en: String): List<UiElementTranslation> {
        return listOf(
            UiElementTranslation(
                languageCode = "de",
                translation = de
            ),
            UiElementTranslation(
                languageCode = "en",
                translation = en
            )
        )
    }

}
