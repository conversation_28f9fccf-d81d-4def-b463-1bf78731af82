<template>
  <div :class="fullscreen ? 'd-flex flex-column justify-center align-center ma-4' : ''">
    <v-card class="pa-4">
      <v-card-title>{{ message || t('errorViews.genericError') }}</v-card-title>
      <slot v-if="$slots.default" />
      <v-card-actions v-else class="justify-end">
        <v-btn @click="push('/')">{{ t('common.back') }}</v-btn>
      </v-card-actions>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

defineProps<{ message?: string | null; fullscreen?: boolean }>();
const { t } = useI18n();
const { push } = useRouter();
</script>
