package com.doorbit.projectconfigurator.bff.infra.config

import org.springframework.cache.Cache
import org.springframework.cache.support.SimpleCacheManager

class DoorbitSimpleCacheManager(val cacheCreatorFunction: (cacheName: String) -> Cache) : SimpleCacheManager() {

    override fun getCache(name: String): Cache {
        return super.getCache(name) ?: cacheCreatorFunction(name).also(::addCacheToCacheManager)
    }

    private fun addCacheToCacheManager(newCache: Cache) {
        val newCacheList = loadCaches().toMutableList().also { it.add(newCache) }
        setCaches(newCacheList)
        initializeCaches()
    }

}