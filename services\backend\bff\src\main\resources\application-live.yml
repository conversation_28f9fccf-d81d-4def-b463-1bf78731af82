logging:
  level:
    root: INFO
    org.springframework: WARN
    org.springframework.security: INFO
    com.doorbit: DEBUG

spring:
  data:
    mongodb:
      uri: mongodb+srv://${MONGO_USER}:${MONGO_PW}@projectconfigurator.yywsqj0.mongodb.net/?retryWrites=true&w=majority
      database: projectconfigurator
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://auth.doorbit.com/login/realms/doorbit/protocol/openid-connect/certs
  graphql:
    schema:
      locations: "file:/schema"

#management:
#  prometheus:
#    metrics:
#      export:
#        enabled: true

server:
  port: 8080

application:
  services:
    listing:
      host: http://listing-microservice-service.listing-microservice/listing
    userprofile:
      host: http://userprofile-microservice-service.userprofile-microservice/userprofile
  slack:
    enabled: true

