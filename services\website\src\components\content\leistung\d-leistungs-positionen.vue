<template>
  <v-data-table-virtual
    v-if="displayContext === 'BACKOFFICE' && leistungDetailsData.leistungDetails.leistungsPositionen.length"
    :headers="headers"
    :items="items"
    :sort-by="[
      { key: 'IS_CUSTOM_CREATED', order: 'desc' },
      { key: 'DISPLAY_NAME', order: 'asc' }
    ]"
  >
    <template #[`item.IS_SELECTED`]="{ item }">
      <div class="d-flex justify-center">
        <v-checkbox
          flat
          color="success"
          hide-details
          density="compact"
          :model-value="item.IS_SELECTED ?? false"
          @update:model-value="
            (selection) => onLeistungspositionSelection({ leistungsPositionId: item.ID, selection: selection! })
          "
        />
      </div>
    </template>
    <template #[`item.DISPLAY_NAME`]="{ item }">
      <div class="d-flex align-center">
        <d-truncated-text :text="item.DISPLAY_NAME" />
        <div class="mx-1" />
        <d-info v-if="item.NOTES || item.DESCRIPTION">
          <div v-if="item.NOTES">{{ t('domain.notes') }}:</div>
          <div class="font-italic">{{ item.NOTES }}</div>
          <div v-if="item.DESCRIPTION" class="pt-2">{{ t('domain.leistungsposition.description') }}:</div>
          <div class="font-italic">{{ item.DESCRIPTION }}</div>
        </d-info>
      </div>
    </template>
    <template #[`item.MARGIN`]="{ item }">
      <div class="d-flex align-center justify-end">
        <div>{{ currencyFormat(item.MARGIN, locale) }}</div>
        <div class="mx-1" />
        <d-info v-if="item.MARGIN_PERCENT">{{ (item.MARGIN_PERCENT * 100).toLocaleString(locale) + '%' }}</d-info>
      </div>
    </template>
    <template #[`item.SECURITY_SURCHARGE`]="{ item }">
      <div class="d-flex align-center justify-end">
        <div>{{ currencyFormat(item.SECURITY_SURCHARGE, locale) }}</div>
        <div class="mx-1" />
        <d-info v-if="item.SECURITY_SURCHARGE_PERCENT">{{
          (item.SECURITY_SURCHARGE_PERCENT * 100).toLocaleString(locale) + '%'
        }}</d-info>
      </div>
    </template>
    <template #[`item.UNIT_PRICE`]="{ item }">
      <span>{{ currencyFormat(item.UNIT_PRICE, locale) }}</span>
    </template>
    <template #[`item.VAT_AMOUNT`]="{ item }">
      <div class="d-flex align-center justify-end">
        <div>{{ currencyFormat(item.VAT_AMOUNT, locale) }}</div>
        <div class="mx-1" />
        <d-info v-if="item.VAT_RATE">{{ (item.VAT_RATE * 100).toLocaleString(locale) + '%' }}</d-info>
      </div>
    </template>
    <template #[`item.OFFER_PRICE_GROSS`]="{ item }">
      <span>{{ currencyFormat(item.OFFER_PRICE_GROSS, locale) }}</span>
    </template>
    <template #[`item.AMOUNT`]="{ item }">
      <div class="d-flex justify-start align-center">
        <span>{{ item.AMOUNT }} {{ item.UNIT }}</span>
        <div class="mx-1" />
        <d-info v-if="item.CALCULATION_FORMULA">
          <div>{{ t('domain.calculationFormula') }}:</div>
          <div class="font-italic">{{ item.CALCULATION_FORMULA }}</div>
        </d-info>
      </div>
    </template>
    <template #[`item.OFFER_PRICE_NET`]="{ item }">
      <span>{{ currencyFormat(item.OFFER_PRICE_NET, locale) }}</span>
    </template>
    <template #[`item.IS_CUSTOM_CREATED`]="{ item }">
      <div v-if="item.IS_CUSTOM_CREATED" class="d-flex justify-center">
        <v-chip color="success" rounded>{{ t('common.new') }}</v-chip>
      </div>
    </template>
    <template #[`item.ACTIONS`]="{ item }">
      <div class="d-flex justify-center">
        <!--TODO: use one form for multiple LP-->
        <d-leistingsposition-update-form
          v-if="displayContext === 'BACKOFFICE'"
          :display-name="item.DISPLAY_NAME!"
          :unit="item.UNIT"
          :description="item.DESCRIPTION"
          :calculation-formula="item.CALCULATION_FORMULA"
          :defaults="{
            unitPrice: item.UNIT_PRICE!,
            vatRatePercent: item.VAT_RATE! * 100,
            amount: item.AMOUNT!,
            margin: item.MARGIN_PERCENT! * 100,
            securitySurcharge: item.SECURITY_SURCHARGE_PERCENT! * 100,
            notes: item.NOTES
          }"
          @submit="onLeistungspositionUpdate({ ...$event, id: item.ID })"
        />
      </div>
    </template>
  </v-data-table-virtual>
</template>

<script setup lang="ts">
import { currencyFormat } from '@/utility/filter';
import {
  ConfigurationTable,
  DGetLeistungDetailsQuery,
  useDLeistungspositionSelectionMutation,
  useDLeistungspositionUpdateMutation
} from '@/adapter/graphql/generated/graphql';
import { computed } from 'vue';
import { LeistungspositionUpdateForm } from '@/components/content/leistung/LeistungspositionUpdateForm';
import { useI18n } from 'vue-i18n';
import { useProjectService } from '@/service/use-project-service';
import DLeistingspositionUpdateForm from '@/components/content/leistung/d-leistingsposition-update-form.vue';
import DInfo from '@/components/ui/d-info.vue';
import DTruncatedText from '@/components/ui/d-truncated-text.vue';

const { locale, t } = useI18n();
const { selectedConfiguration, displayContext } = useProjectService();
const { mutate: updateLeistungsposition } = useDLeistungspositionUpdateMutation();
const { mutate: selectLeistungsposition } = useDLeistungspositionSelectionMutation();

const props = defineProps<{
  leistungDetailsData: DGetLeistungDetailsQuery;
}>();
const emits = defineEmits<{ newTableData: [data?: ConfigurationTable] }>();

const headers = [
  { title: '', key: 'IS_SELECTED' },
  { title: t('domain.displayName'), key: 'DISPLAY_NAME' },
  { title: t('domain.amount'), key: 'AMOUNT', align: 'start' as never },
  { title: t('domain.unitPrice'), key: 'UNIT_PRICE', align: 'end' as never },
  { title: t('domain.securitySurcharge'), key: 'SECURITY_SURCHARGE', align: 'end' as never },
  { title: t('domain.margin'), key: 'MARGIN', align: 'end' as never },
  { title: t('domain.offerPriceNet'), key: 'OFFER_PRICE_NET', align: 'end' as never },
  { title: t('domain.vatAmount'), key: 'VAT_AMOUNT', align: 'end' as never },
  { title: t('domain.offerPriceGross'), key: 'OFFER_PRICE_GROSS', align: 'end' as never },
  { title: '', key: 'IS_CUSTOM_CREATED', align: 'center' as never },
  { title: '', key: 'ACTIONS' }
];

const items = computed(() =>
  props.leistungDetailsData.leistungDetails.leistungsPositionen.map((lp) => ({
    IS_SELECTED: lp.cells.find((col) => col.key === 'IS_SELECTED')?.booleanValue,
    DISPLAY_NAME: lp.cells.find((col) => col.key === 'DISPLAY_NAME')?.stringValue,
    DESCRIPTION: lp.cells.find((col) => col.key === 'DESCRIPTION')?.stringValue,
    AMOUNT: lp.cells.find((col) => col.key === 'AMOUNT')?.doubleValue,
    UNIT: lp.cells.find((col) => col.key === 'UNIT')?.stringValue,
    CALCULATION_FORMULA: lp.cells.find((col) => col.key === 'CALCULATION_FORMULA')?.stringValue,
    UNIT_PRICE: lp.cells.find((col) => col.key === 'UNIT_PRICE')?.doubleValue,
    SECURITY_SURCHARGE: lp.cells.find((col) => col.key === 'SECURITY_SURCHARGE')?.doubleValue,
    SECURITY_SURCHARGE_PERCENT: lp.cells.find((col) => col.key === 'SECURITY_SURCHARGE_PERCENT')?.doubleValue,
    MARGIN: lp.cells.find((col) => col.key === 'MARGIN')?.doubleValue,
    MARGIN_PERCENT: lp.cells.find((col) => col.key === 'MARGIN_PERCENT')?.doubleValue,
    OFFER_PRICE_NET: lp.cells.find((col) => col.key === 'OFFER_PRICE_NET')?.doubleValue,
    VAT_AMOUNT: lp.cells.find((col) => col.key === 'VAT_AMOUNT')?.doubleValue,
    VAT_RATE: lp.cells.find((col) => col.key === 'VAT_RATE')?.doubleValue,
    OFFER_PRICE_GROSS: lp.cells.find((col) => col.key === 'OFFER_PRICE_GROSS')?.doubleValue,
    IS_CUSTOM_CREATED: lp.cells.find((col) => col.key === 'IS_CUSTOM_CREATED')?.booleanValue,
    NOTES: lp.cells.find((col) => col.key === 'NOTES')?.stringValue,
    ID: lp.id
  }))
);

async function onLeistungspositionUpdate({
  id,
  unitPrice,
  vatRatePercent,
  amount,
  margin,
  securitySurcharge,
  notes
}: LeistungspositionUpdateForm & { id: string }) {
  const result = await updateLeistungsposition({
    configurationId: selectedConfiguration.value!.configurationId,
    leistungId: props.leistungDetailsData.leistungDetails.id,
    leistungsPositionId: id,
    unitPrice,
    vatRatePercent,
    amount,
    margin,
    securitySurcharge,
    notes
  });
  emits('newTableData', result?.data?.configureLeistungsPosition as ConfigurationTable | undefined);
}

async function onLeistungspositionSelection({
  leistungsPositionId,
  selection
}: {
  leistungsPositionId: string;
  selection: boolean;
}) {
  const result = await selectLeistungsposition({
    configurationId: selectedConfiguration.value!.configurationId,
    leistungId: props.leistungDetailsData.leistungDetails.id,
    leistungsPositionId,
    selection
  });
  emits('newTableData', result?.data?.selectLeistungsPosition as ConfigurationTable | undefined);
}
</script>

<style scoped></style>
