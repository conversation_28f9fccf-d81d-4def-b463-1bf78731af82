<?xml version="1.0"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0">

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.doorbit</groupId>
    <artifactId>backend-bff</artifactId>

    <parent>
        <groupId>com.doorbit.projectconfigurator</groupId>
        <artifactId>backend-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <kotlin.version>1.9.23</kotlin.version>
        <java.version>21</java.version>
        <compiler-plugin.version>3.13.0</compiler-plugin.version>
        <logback-classic.version>0.1.5</logback-classic.version>
        <logstash-logbackencoder.version>7.4</logstash-logbackencoder.version>
        <mojo.version>3.5.0</mojo.version>
        <findbugs.version>3.0.2</findbugs.version>
        <shedlock.version>5.13.0</shedlock.version>
        <javax.annotations.version>1.3.2</javax.annotations.version>
        <openapi.version>7.4.0</openapi.version>
    </properties>

    <dependencyManagement>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.doorbit.projectconfigurator</groupId>
            <artifactId>backend-project-configurator</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!-- Spring -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-graphql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.boot</groupId>-->
        <!--            <artifactId>spring-boot-starter-security</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!-- Bean validation support for Spring -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>${javax.annotations.version}</version>
        </dependency>

        <!-- ### -->
        <!-- Logback config support -->
        <dependency>
            <groupId>ch.qos.logback.contrib</groupId>
            <artifactId>logback-json-classic</artifactId>
            <version>${logback-classic.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback.contrib</groupId>
            <artifactId>logback-jackson</artifactId>
            <version>${logback-classic.version}</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logbackencoder.version}</version>
        </dependency>
        <!-- ### -->
        <!-- Cache framework -->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <!-- ### -->
        <!-- Kotlin -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <!-- ### -->

        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>${findbugs.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>0.2.6</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <!-- ### -->
        <!-- We don't use logback directly but abstraction framework sl4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>src/main/kotlin</sourceDirectory>

        <finalName>app</finalName>
        <plugins>
            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.doorbit.projectconfigurator.bff.ApplicationKt</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- OpenAPI generator -->
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi.version}</version>

                <executions>
                    <!-- Server -->
                    <!--                    <execution>-->
                    <!--                        <id>listing-api</id>-->
                    <!--                        <goals>-->
                    <!--                            <goal>generate</goal>-->
                    <!--                        </goals>-->
                    <!--                        <configuration>-->
                    <!--                            <generatorName>kotlin-spring</generatorName>-->
                    <!--                            <library>spring-boot</library>-->
                    <!--                            <inputSpec>${project.basedir}/../../api/rest/listing-api.yaml</inputSpec>-->
                    <!--                            <apiPackage>com.doorbit.listing.infra.adapter.api</apiPackage>-->
                    <!--                            <modelPackage>com.doorbit.listing.infra.adapter.api.dto</modelPackage>-->
                    <!--                            <invokerPackage>com.doorbit.listing.infra.adapter.api</invokerPackage>-->
                    <!--                            <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>-->
                    <!--                            <modelNameSuffix>Dto</modelNameSuffix>-->

                    <!--                            <generateApis>true</generateApis>-->
                    <!--                            <generateModels>true</generateModels>-->

                    <!--                            <generateApiTests>false</generateApiTests>-->
                    <!--                            <generateModelTests>false</generateModelTests>-->

                    <!--                            <generateApiDocumentation>false</generateApiDocumentation>-->
                    <!--                            <generateModelDocumentation>false</generateModelDocumentation>-->

                    <!--                            <configOptions>-->
                    <!--                                <annotationLibrary>none</annotationLibrary>-->
                    <!--                                <documentationProvider>none</documentationProvider>-->
                    <!--                                <serializationLibrary>jackson</serializationLibrary>-->
                    <!--                                <enumPropertyNaming>UPPERCASE</enumPropertyNaming>-->
                    <!--                                <useBeanValidation>true</useBeanValidation>-->
                    <!--                                <useSwaggerUI>false</useSwaggerUI>-->
                    <!--                                <useTags>true</useTags>-->
                    <!--                                <exceptionHandler>false</exceptionHandler>-->
                    <!--                                <interfaceOnly>true</interfaceOnly>-->
                    <!--                                <useSpringBoot3>true</useSpringBoot3>-->
                    <!--                            </configOptions>-->
                    <!--                            <importMappings>DateTime=java.time.Instant</importMappings>-->
                    <!--                            <typeMappings>DateTime=java.time.Instant</typeMappings>-->
                    <!--                        </configuration>-->
                    <!--                    </execution>-->

                </executions>
            </plugin>

        </plugins>
    </build>
</project>
