import { ComposerTranslation, createI18n, DefineLocaleMessage } from 'vue-i18n';
import { LSF__LANGUAGE_CODE } from '@/service/local-storage/local-storage';
import deMessages from '@/adapter/vue-i18n/messages/de-messages';
import enMessages from '@/adapter/vue-i18n/messages/en-messages';
import esMessages from '@/adapter/vue-i18n/messages/es-messages';
import deNumberFormats from '@/adapter/vue-i18n/number-formats/de-number-formats';
import esNumberFormats from '@/adapter/vue-i18n/number-formats/es-number-formats';
import enNumberFormats from '@/adapter/vue-i18n/number-formats/en-number-formats';
import { RemoveIndexSignature } from '@intlify/core-base';
import deDateTimeFormats from '@/adapter/vue-i18n/date-time-formats/de-date-time-formats';
import enDateTimeFormats from '@/adapter/vue-i18n/date-time-formats/en-date-time-formats';
import esDateTimeFormats from '@/adapter/vue-i18n/date-time-formats/es-date-time-formats';

//https://github.com/intlify/bundle-tools/tree/main/packages/unplugin-vue-i18n
export default createI18n<false>({
  legacy: false,
  locale: LSF__LANGUAGE_CODE.state.value,
  escapeParameter: true,
  escapeParameterHtml: true,
  fallbackLocale: 'de',
  messages: {
    de: deMessages,
    en: enMessages,
    es: esMessages,
    ca: esMessages
  },
  numberFormats: {
    de: deNumberFormats,
    en: enNumberFormats,
    es: esNumberFormats,
    ca: esNumberFormats
  },
  datetimeFormats: {
    de: deDateTimeFormats,
    en: enDateTimeFormats,
    es: esDateTimeFormats,
    ca: esDateTimeFormats
  }
});

export type Translator = ComposerTranslation<
  object,
  unknown,
  RemoveIndexSignature<{
    [K in keyof DefineLocaleMessage]: DefineLocaleMessage[K];
  }>
>;
