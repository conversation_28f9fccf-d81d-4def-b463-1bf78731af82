package com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.productdatabase

import com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.SlackAdapter
import com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.airtable.dto.RecordDto
import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.*
import org.springframework.stereotype.Component
import java.lang.Thread.startVirtualThread

@Component
class ProductDatabaseValidator(
    private val slackAdapter: SlackAdapter
) {

    val constraintViolations = mutableListOf<String>()

    fun vergabeEinheitenValidatorFilter(it: RecordDto): Boolean {
        val name = it.stringOrNull("Name")
        if (name == null || name.isEmpty()) {
            return collectConstraintViolation("Vergabeeinheit ${it.id} wird ignoriert, weil Feld 'Name' leer ist.")
        }

        if (it.arrayOfStringsOrNull("Komponenten") == null) {
            return collectConstraintViolation("Vergabeeinheit ${it.id} ($name) wird ignoriert, weil Feld 'Komponenten' leer ist und die Vergabeeinheit sonst ohne Inhalt im Frontend erscheinen würde.")
        }

        return true
    }

    fun komponentenValidatorFilter(recordDto: RecordDto): Boolean {
        if (recordDto.stringOrNull("Name") == null || recordDto.stringOrNull("Name")!!.isEmpty()) {
            return collectConstraintViolation("Komponente ${recordDto.id} wird ignoriert, weil Feld 'Name' leer ist.")
        }

        return true
    }

    fun leistungValidatorFilter(recordDto: RecordDto): Boolean {
        val name = recordDto.stringOrNull("Name")
        if (name == null || name.isEmpty()) {
            return collectConstraintViolation("Leistung ${recordDto.id} wird ignoriert, weil Feld 'Name' leer ist.")
        }

        if (recordDto.arrayOfStringsOrNull("Komponente") == null) {
            return collectConstraintViolation("Leistung ${recordDto.id} ($name) wird ignoriert, weil Feld 'Komponente' leer und der Leistung daher keiner Komponente zugeordnet werden kann.")
        }

        val status = recordDto.stringOrNull("Konfiguratorstatus")

        if (status == null || status.isEmpty()) {
            return collectConstraintViolation("Leistung ${recordDto.id} ($name) wird ignoriert, weil Feld 'Konfiguratorstatus' leer ist.")
        }

        if (status != "Bereit für Export" && status != "Live") {
            return collectConstraintViolation("Leistung ${recordDto.id} ($name) wird ignoriert, weil Feld 'Konfiguratorstatus' nicht 'Bereit für Export' oder 'Live' ist, sondern: $status.")
        }

        return true
    }

    fun leistungsPositionenValidatorFilter(recordDto: RecordDto): Boolean {
        if (recordDto.stringOrNull("Name") == null || recordDto.stringOrNull("Name")!!.isEmpty()) {
            return collectConstraintViolation("Leistungsposition ${recordDto.id} wird ignoriert, weil Feld 'Name' leer ist.")
        }

        if (recordDto.doubleOrNull("Einheitspreis") == null) {
            return collectConstraintViolation("Leistungsposition ${recordDto.id} wird ignoriert, weil Feld 'Einheitspreis' leer ist.")
        }

        return true
    }

    fun collectConstraintViolation(msg: String): Boolean {
        constraintViolations.add("- $msg\n")
        return false
    }

    fun finishConstraintViolations(importedId: String?) {
        startVirtualThread {
            if (constraintViolations.isNotEmpty()) {
                val message = "Zusammenfassung des Produktdatenbank-Imports:\n" +
                        "Neue Datenbank: '$importedId.\n'" +
                        constraintViolations.joinToString("")
                LOGGER.w { message }
                slackAdapter.postMessage(message)
            } else {
                val message = "Produktdatenbank-Import erfolgreich: '$importedId'"
                LOGGER.w { message }
                slackAdapter.postMessage(message)
            }
        }
    }

    fun addError(error: Exception, message: String) {
        constraintViolations.add("ACHTUNG: $message\n")
        LOGGER.e(error) { "Error while importing global config from AirTable. ${error.message}" }
    }

    companion object : WithLogger()

}