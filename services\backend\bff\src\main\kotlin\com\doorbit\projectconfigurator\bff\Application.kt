package com.doorbit.projectconfigurator.bff

import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.ComponentScan

@EnableCaching
@ComponentScan("com.doorbit.projectconfigurator")
@SpringBootApplication
class Application

fun main(args: Array<String>) {
    SpringApplication(Application::class.java).run(*args)
}
