<template>
  <svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M51.2957 31.9586L34.3251 14.988C31.982 12.6449 28.183 12.6449 25.8398 14.988L8.86929 31.9586C6.52614 34.3017 6.52614 38.1007 8.86929 40.4438L25.8398 57.4144C28.183 59.7576 31.982 59.7576 34.3251 57.4144L51.2957 40.4438C53.6388 38.1007 53.6388 34.3017 51.2957 31.9586Z"
      :stroke="current.colors.primaryLight as string"
      stroke-width="3"
      stroke-miterlimit="10"
    />
    <path
      d="M63.1307 31.5294L46.1602 14.5588C43.817 12.2157 40.018 12.2157 37.6749 14.5588L20.7043 31.5294C18.3612 33.8725 18.3612 37.6715 20.7043 40.0146L37.6749 56.9852C40.018 59.3284 43.817 59.3284 46.1602 56.9852L63.1307 40.0146C65.4739 37.6715 65.4739 33.8725 63.1307 31.5294Z"
      :fill="current.colors.primary"
      :stroke="current.colors.primary"
      stroke-width="3"
      stroke-miterlimit="10"
    />
    <path d="M41.85 26.8499V44.8499" stroke="white" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round" />
    <path d="M32.85 35.8499H50.85" stroke="white" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round" />
  </svg>
</template>
<script setup lang="ts">
import { useTheme } from 'vuetify';
const { current } = useTheme();
</script>
