// noinspection ES6UnusedImports
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { DefineLocaleMessage } from 'vue-i18n';

declare module 'vue-i18n' {
  declare interface DefineLocaleMessage {
    $vuetify: {
      input: {
        clear: string;
      };
      carousel: {
        prev: string;
        next: string;
        ariaLabel: {
          delimiter: string;
        };
      };
      open: string;
      close: string;
      badge: string;
      dataFooter: {
        pageText: string;
        itemsPerPageText: string;
      };
      noDataText: string;
    };
    domain: {
      massedaten: string;
      preSelection: string;
      notes: string;
      vergabeEinheitenFilters: {
        searchLabel: string;
        filterLabel: string;
        filterItems: {
          selected: string;
          invalid: string;
          incomplete: string;
          configured: string;
          partiallyConfigured: string;
          notConfigured: string;
        };
      };
      costOverview: string;
      globallyAvailable: {
        globallyAvailable: string;
        notGloballyAvailable: string;
      };
      displayName: string;
      amount: string;
      priceGross: string;
      unitPrice: string;
      securitySurcharge: string;
      margin: string;
      offerPriceNet: string;
      vatAmount: string;
      vatRate: string;
      netCosts: string;
      totalProjectCosts: string;
      offerPriceGross: string;
      calculationFormula: string;
      contractorName: string;
      dataSource: {
        dataSource: string;
        created: string;
        imported: string;
      };
      selfService: string;
      eligible: string;
      notEligible: string;
      dataSheet: string;
      execution: string;
      vergabeEinheit: {
        component: string;
        grossQuotePrice: string;
        netPlanningCosts: string;
      };
      leistung: {
        selected: string;
        notSelected: string;
        newLeistung: string;
        leistung: string;
        uValue: string;
        manufacturer: string;
        offerPriceGross: string;
        offerPrice: string;
        leistungType: string;
        unit: string;
        types: {
          ZERO_VARIANT: string;
          ALTERNATIVE: string;
          ADD_ON: string;
        };
      };
      leistungsposition: {
        takeFromMassedaten: string;
        creationInstruction: string;
        unitCreateOrSelect: string;
        leistungspositionen: string;
        newLeistungsposition: string;
        description: string;
        unitPrice: string;
        dinNumber: string;
      };
      configurations: {
        archiveConfirm: string;
        createNewConfig: string;
        createNewError: string;
        databaseVersion: string;
        newDatabaseVersion: string;
        configLabel: string;
        noneCreated: string;
        createFirst: string;
        noDbVersions: string;
      };
    };
    common: {
      yes: string;
      no: string;
      ok: string;
      new: string;
      save: string;
      cancel: string;
      delete: string;
      prev: string;
      next: string;
      download: string;
      redirectMessage: string;
      pleaseWait: string;
      back: string;
    };
    errorViews: {
      notFound: string;
      genericError: string;
      apolloError: string;
      reload: string;
      consultAdmin: string;
      missingListingId: string;
      projectDataNotLoaded: string;
      serverErrorLeistungCreate: string;
      noMassedaten: string;
    };
    formRules: {
      required: string;
    };
  }
}
