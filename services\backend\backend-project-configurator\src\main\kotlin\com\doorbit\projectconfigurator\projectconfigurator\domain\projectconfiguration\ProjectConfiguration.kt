package com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration

import com.doorbit.projectconfigurator.projectconfigurator.domain.LeistungId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProductDatabaseId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProjectConfigurationId
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.DbLeistung
import java.math.BigDecimal
import java.time.Instant

data class ProjectConfiguration(
    val id: ProjectConfigurationId,
    val name: String,
    val productDatabaseId: ProductDatabaseId,
    val databaseVersion: String,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant? = null,
    val leistungen: Leistungen = Leistungen(),
    val customCreatedLeistung: MutableList<LeistungId> = mutableListOf(),
    val customCreatedLeistungsPositionen: MutableList<LeistungsPositionId> = mutableListOf(),
    val deletedLeistungen: MutableList<String> = mutableListOf(),
    val deletedLeistungsPositionen: MutableList<String> = mutableListOf(),
    var calculation: Calculation = Calculation.EMPTY,
    val archived: Boolean = false
) {
    fun setArchived() : ProjectConfiguration {
        return copy(archived = true)
    }

    fun addCustomLeistungId(leistungId: LeistungId): ProjectConfiguration {
        val updated = copy(customCreatedLeistung = (customCreatedLeistung + leistungId).toMutableList())
        updated.calculate()
        return updated
    }

    fun addCustomLeistungsPositionId(recordId: String): ProjectConfiguration {
        val updated = copy(customCreatedLeistungsPositionen = (customCreatedLeistungsPositionen + recordId).toMutableList())
        return updated
    }

    fun calculate() {
        leistungen.calculate()

        this.calculation = Calculation(
            offerPriceGross = leistungen.totalOfferPriceGross(),
            offerPriceNet = leistungen.totalOfferPriceNet(),
            vatAmount = leistungen.totalVatAmount(),
            netCosts = leistungen.totalNetCosts(),
            margin = leistungen.totalMargin(),
            securitySurcharge = leistungen.totalSecuritySurcharge()
        )
    }

    fun netCostsByLeistungIds(alleLeistungen: List<String>): BigDecimal {
        return leistungen.totalNetCostsByLeistungIds(alleLeistungen)
    }

    fun offerPriceGrossByLeistungIds(allLeistungIds: List<String>): BigDecimal {
        return leistungen.totalOfferPriceGrossByLeistungIds(allLeistungIds)
    }

    fun offerPriceNetByLeistungIds(allLeistungenIds: List<String>): BigDecimal {
        return leistungen.totalOfferPriceNetByLeistungIds(allLeistungenIds)
    }

    fun marginByLeistungIds(allLeistungenIds: List<String>): BigDecimal {
        return leistungen.totalMarginByLeistungIds(allLeistungenIds)
    }

    fun vatAmountByLeistungIds(allLeistungenIds: List<String>): BigDecimal {
        return leistungen.totalVatAmountByLeistungIds(allLeistungenIds)
    }

    fun securitySurchargeByLeistungIds(allLeistungenIds: List<String>): BigDecimal {
        return leistungen.totalSecuritySurchargeByLeistungIds(allLeistungenIds)
    }

    fun selectAllLeistungsPositionen(leistungId: LeistungId, dbLeistung: DbLeistung) {
        dbLeistung.leistungsPositionen.filter { !it.isCustomCreatedLeistungsPosition || customCreatedLeistungsPositionen.contains(it.recordId) }
            .forEach { leistungen.selectLeistungsPosition(leistungId, it.recordId, true, dbLeistung) }
    }

    fun deleteLeistung(leistungId: LeistungId) : ProjectConfiguration {
        val deleted = leistungen.deleteLeistung(leistungId)
        customCreatedLeistung.remove(leistungId)
        deleted?.let {
            it.configuredLeistungsPositionen.forEach { leistungsPosition -> customCreatedLeistungsPositionen.remove(leistungsPosition.id) }
        }

        deletedLeistungen.add(leistungId)
        return this
    }

    fun deleteLeistungsPosition(leistungId: LeistungId, leistungsPositionId: LeistungsPositionId): ProjectConfiguration {
        leistungen.deleteLeistungsPosition(leistungId, leistungsPositionId)
        customCreatedLeistungsPositionen.remove(leistungsPositionId)
        deletedLeistungsPositionen.add(leistungsPositionId)
        return this
    }
}