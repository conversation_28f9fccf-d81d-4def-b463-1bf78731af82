logging:
  level:
    com:
      doorbit: TRACE
    org:
      springframework: WARN

spring:
  data:
    mongodb:
      uri: **********************************************************
      database: projectconfigurator
#  security:
#    oauth2:
#      resourceserver:
#        jwt:
#          issuer-uri: https://auth-integ.doorbit.com/login/realms/doorbit
#          jwk-set-uri: https://auth-integ.doorbit.com/login/realms/doorbit/protocol/openid-connect/certs
  jpa:
    show-sql: false
    properties:
      hibernate.format_sql: false

  graphql:
    graphiql:
      enabled: true
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
    tools:
      introspection-enabled: true

application:
  cloudflare-cookie: "CF_Authorization=eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlM2E1ODdhNWMxNTU2YTNiMDI0NmQyYTQ1MThhYzY3ZjFlNDY3OTg1OTYzNWI2MGMzODM0MGM4NDAwNTBiMzMifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.VaGs9gLcH3aKX_1psrBiFlAjwUJbWrrxlpfoi_wyTgX1BDRRwQBczNieFvZfT-JKWyFzgQDsupWF2RGCPB1TQmLbzG9LPCQfFhnASBJ-ucwOZbMxoIZJrZSKEEfSQIsB-T8XJavXauZ7d4GbTsXJQ1HSw8y1-DkR6tpy3HfLkQXsB193JioX2UTaABuljjL1ANbTezVQV2pxIgX_G2nxSv1vF9V_f_tnBSlV3nk1_6Jv0LA7qb78itYlhBbg_pHf3pGdr10epyC-qQxFu9-Um67lL0iG6EbK2-RA1u2fNbsQDHwzTcC2Qh7iY_ZdMt30CWJNgZL09YuDEmVoNv2P5g"
  services:
    listing:
      host: https://integ.doorbit.com/listing
    userprofile:
      host: https://integ.doorbit.com/userprofile

management:
  endpoint:
    health:
      show-details: always
    features:
      enabled: true
    configprops:
      enabled: true
    httpexchanges:
      enabled: true
    info:
      enabled: true
  endpoints:
    enabled-by-default: true
