<template>
  <v-icon
    :icon="completenessState === 'COMPLETELY_CONFIGURED' ? mdiCheckboxMarkedCircle : mdiCircleOutline"
    :color="completenessState === 'COMPLETELY_CONFIGURED' ? 'success' : 'unset'"
  />
</template>
<script setup lang="ts">
import { mdiCheckboxMarkedCircle, mdiCircleOutline } from '@mdi/js';
import { CompletenessState } from '@/adapter/graphql/generated/graphql';

defineProps<{ completenessState?: CompletenessState }>();
</script>
