package com.doorbit.projectconfigurator.bff.infra.config.security

import jakarta.servlet.Filter
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.web.SecurityFilterChain

/**
 * This config basically disables Spring Security.
 */
@Configuration
//@Profile("!local")
class SeurityConfigDisabled {

    @Bean
    fun springWebFilterChainDisabled(http: HttpSecurity): SecurityFilterChain = object : SecurityFilterChain {
        override fun matches(request: HttpServletRequest?): Boolean = false

        override fun getFilters(): MutableList<Filter> = mutableListOf()
    }
}
