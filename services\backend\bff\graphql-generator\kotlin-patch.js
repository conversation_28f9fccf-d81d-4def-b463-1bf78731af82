const fs = require('fs');
const path = require('path');

const loadGeneratedPath = path.resolve(__dirname, '../src/main/kotlin/com/doorbit/projectconfigurator/bff/infra/api/dto/GraphQLDTOs.kt');

fs.writeFileSync(loadGeneratedPath, fs
    .readFileSync(loadGeneratedPath, 'utf8')
    // .replace(/data class ListingSearchResult\(\n(.+)val listings: List<Listing>,/s, 'data class ListingSearchResult(\n$1')
    // .replaceAll(/val adminBoundary: AdminBoundary\?,/mg, '')
)
