package com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration

import java.math.BigDecimal

data class Calculation(
    val offerPriceGross: BigDecimal = BigDecimal.ZERO,
    val offerPriceNet: BigDecimal = BigDecimal.ZERO,
    val vatAmount: BigDecimal = BigDecimal.ZERO,
    val netCosts: BigDecimal = BigDecimal.ZERO,
    val margin : BigDecimal = BigDecimal.ZERO,
    val securitySurcharge : BigDecimal = BigDecimal.ZERO,
) {

    companion object {
        val EMPTY = Calculation(BigDecimal.ZERO, BigDecimal.ZERO)
    }

}