package com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.config

import com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.airtable.AirtableProductDatabaseApi
import com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.listing.BuildingApi
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.http.client.ClientHttpRequestFactory
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.web.client.RestClient

@Configuration
class ApiConfig(
    @Value("\${application.cloudflare-cookie}") private val cfAuthorizationCookie: String
) {

    @Bean
    fun productDatabaseApi(
        @Value(value = "\${application.productDatabase.airtable.token}") tokenAuth: String
    ): AirtableProductDatabaseApi {
        return AirtableProductDatabaseApi(
            httpClient(
                baseUrl = "https://api.airtable.com/v0",
                bearerToken = tokenAuth,
                readTimeout = 7_000,
            )
        )
    }

    @Bean
    fun buildingApi(
        @Value(value = "\${application.services.listing.host}") listingHost: String
    ): BuildingApi {
        return BuildingApi(
            httpClient(
                baseUrl = listingHost,
                readTimeout = 1_000,
            )
        )
    }

    private fun httpClient(
        baseUrl: String,
        bearerToken: String? = null,
        readTimeout: Int = 1_000,
        connectTimeout: Int = 250
    ): RestClient {
        val requestFactory = RestClient.builder()
            .baseUrl(baseUrl)
            .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
            .requestFactory(httpClientWithCustomTimeoutRequestFactory(readTimeout, connectTimeout))

        if (bearerToken != null) {
            requestFactory.defaultHeaders { it.setBearerAuth(bearerToken) }
        }

        if (cfAuthorizationCookie.isNotEmpty()) {
            requestFactory.defaultHeader(HttpHeaders.COOKIE, cfAuthorizationCookie)
        }

        return requestFactory.build()
    }

    private fun httpClientWithCustomTimeoutRequestFactory(readTimeout: Int, connectTimeout: Int): ClientHttpRequestFactory {
        val requestFactory = SimpleClientHttpRequestFactory()
        requestFactory.setReadTimeout(readTimeout)
        requestFactory.setConnectTimeout(connectTimeout)
        return requestFactory
    }

}