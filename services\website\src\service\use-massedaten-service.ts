import { inject, InjectionKey, provide, ref } from 'vue';
import { DGetMassedatenQuery, useDGetMassedatenLazyQuery } from '@/adapter/graphql/generated/graphql';

export type MassedatenService = ReturnType<typeof initializeMassedatenService>;

const massedatenServiceKey = Symbol('massedatenService') as InjectionKey<MassedatenService>;

export function initializeMassedatenService(provideFn: typeof provide) {
  const massedaten = ref<DGetMassedatenQuery | null>(null);

  const {
    error: getMassedatenError,
    loading: areMassedatenLoading,
    variables: getMassedatenVariables,
    onResult: onMassedatenLoaded,
    load: loadMassedaten,
    refetch: refetchMassedaten
  } = useDGetMassedatenLazyQuery();

  onMassedatenLoaded((res) => {
    massedaten.value = res.data;
  });

  const massedatenService = {
    massedaten,
    getMassedatenError,
    areMassedatenLoading,
    getMassedatenVariables,
    loadMassedaten,
    refetchMassedaten
  };
  provideFn<MassedatenService>(massedatenServiceKey, massedatenService);
  return massedatenService;
}

export function useMassedatenService(): MassedatenService {
  return inject<MassedatenService>(massedatenServiceKey)!;
}
