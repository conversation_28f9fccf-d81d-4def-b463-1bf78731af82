import { getTranslation } from '@/utility/get-translation';
import {
  Leistung,
  LeistungsPosition,
  VergabeEinheit,
  VergabeEinheitKomponente
} from '@/adapter/graphql/generated/graphql';

export function getVergabeEinheitenTableItem({
  locale,
  vergabeEinheit,
  komponente,
  leistung,
  leistungsPosition,
  moduleToColorMap
}: {
  locale: string;
  vergabeEinheit: VergabeEinheit;
  komponente?: VergabeEinheitKomponente;
  leistung?: Leistung;
  leistungsPosition?: LeistungsPosition;
  moduleToColorMap: { [moduleName: string]: string };
}) {
  const vergabeEinheitName = getTranslation(locale, vergabeEinheit.displayName);
  return {
    moduleName: vergabeEinheit.moduleName,
    moduleSeq: (vergabeEinheit as VergabeEinheit & { moduleSeq?: number })?.moduleSeq,
    moduleColor: moduleToColorMap[vergabeEinheit.moduleName],
    vergabeEinheitId: vergabeEinheit.id,
    vergabeEinheitName: vergabeEinheitName,
    vergabeEinheitSeq: (vergabeEinheit as VergabeEinheit & { vergabeEinheitSeq?: number })?.vergabeEinheitSeq,
    vergabeEinheitHasWarnings: vergabeEinheit.hasWarnings,
    vergabeEinheitNetCosts: vergabeEinheit.netCosts,
    vergabeEinheitOfferPriceNet: vergabeEinheit.offerPriceNet,
    vergabeEinheitOfferPriceGross: vergabeEinheit.offerPriceGross,
    vergabeEinheitCompleteness: vergabeEinheit.completenessState,
    vergabeEinheitSecuritySurcharge: vergabeEinheit.securitySurcharge,
    vergabeEinheitMargin: vergabeEinheit.margin,
    vergabeEinheitVatAmount: vergabeEinheit.vatAmount,
    komponenteId: komponente?.id,
    komponenteName: getTranslation(locale, komponente?.displayName),
    komponenteSeq: (komponente as VergabeEinheitKomponente & { komponenteSeq?: number })?.komponenteSeq,
    komponenteHasWarnings: komponente?.hasWarnings,
    komponenteNetCosts: komponente?.netCosts,
    komponenteOfferPriceGross: komponente?.offerPriceGross,
    komponenteCompleteness: komponente?.completenessState,
    komponenteSecuritySurcharge: komponente?.securitySurcharge,
    komponenteMargin: komponente?.margin,
    komponenteVatAmount: komponente?.vatAmount,
    leistungType: leistung?.type,
    leistungId: leistung?.id,
    leistungName: leistung?.cells.find((cell) => cell.key === 'DISPLAY_NAME')?.stringValue,
    leistungSeq: (leistung as Leistung & { leistungSeq?: number })?.leistungSeq,
    leistungSelected: leistung?.isSelected,
    leistungOfferPriceGross: leistung?.cells.find((cell) => cell.key === 'OFFER_PRICE_GROSS')?.doubleValue,
    leistungNetCosts: leistung?.cells.find((cell) => cell.key === 'NET_COSTS')?.doubleValue,
    leistungExecution: leistung?.cells.find((cell) => cell.key === 'EXECUTION')?.stringValue,
    leistungEligible: leistung?.cells.find((cell) => cell.key === 'ELIGIBLE')?.booleanValue,
    leistungSecuritySurcharge: leistung?.cells.find((cell) => cell.key === 'SECURITY_SURCHARGE')?.doubleValue,
    leistungMargin: leistung?.cells.find((cell) => cell.key === 'MARGIN')?.doubleValue,
    leistungVatAmount: leistung?.cells.find((cell) => cell.key === 'VAT_AMOUNT')?.doubleValue,
    leistungsPositionId: leistungsPosition?.id,
    leistungsPositionName: leistungsPosition?.cells.find((cell) => cell.key === 'DISPLAY_NAME')?.stringValue,
    leistungsPositionSeq: (leistungsPosition as LeistungsPosition & { leistungsPositionSeq?: number })
      ?.leistungsPositionSeq,
    leistungsPositionOfferPriceGross: leistungsPosition?.cells.find((cell) => cell.key === 'OFFER_PRICE_GROSS')
      ?.doubleValue,
    leistungsPositionOfferPriceNet: leistungsPosition?.cells.find((cell) => cell.key === 'OFFER_PRICE_NET')
      ?.doubleValue,
    leistungsPositionSelected: leistungsPosition?.cells.find((cell) => cell.key === 'IS_SELECTED')?.booleanValue,
    leistungsPositionAmount: leistungsPosition?.cells.find((cell) => cell.key === 'AMOUNT')?.doubleValue,
    leistungsPositionUnit: leistungsPosition?.cells.find((cell) => cell.key === 'UNIT')?.stringValue,
    leistungsPositionFormula: leistungsPosition?.cells.find((cell) => cell.key === 'CALCULATION_FORMULA')?.stringValue,
    leistungsPositionUnitPrice: leistungsPosition?.cells.find((cell) => cell.key === 'UNIT_PRICE')?.doubleValue,
    leistungsPositionNetCosts: leistungsPosition?.cells.find((cell) => cell.key === 'NET_COSTS')?.doubleValue,
    leistungsPositionSecuritySurcharge: leistungsPosition?.cells.find((cell) => cell.key === 'SECURITY_SURCHARGE')
      ?.doubleValue,
    leistungsPositionSecuritySurchargePercent: leistungsPosition?.cells.find(
      (cell) => cell.key === 'SECURITY_SURCHARGE_PERCENT'
    )?.doubleValue,
    leistungsPositionMargin: leistungsPosition?.cells.find((cell) => cell.key === 'MARGIN')?.doubleValue,
    leistungsPositionMarginPercent: leistungsPosition?.cells.find((cell) => cell.key === 'MARGIN_PERCENT')?.doubleValue,
    leistungsPositionVatAmount: leistungsPosition?.cells.find((cell) => cell.key === 'VAT_AMOUNT')?.doubleValue,
    leistungsPositionVatRate: leistungsPosition?.cells.find((cell) => cell.key === 'VAT_RATE')?.doubleValue
  };
}
