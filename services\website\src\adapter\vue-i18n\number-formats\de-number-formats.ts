import { DefineNumberFormat } from 'vue-i18n';

//TODO: hier müssen noch die korrekten unicode zeichen verwendet werden. z.b. für Meter usw.
const deNumberFormats: DefineNumberFormat = {
  currency: {
    style: 'currency',
    currency: 'EUR',
    notation: 'standard',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  },
  decimal: {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }
};

export default deNumberFormats;
