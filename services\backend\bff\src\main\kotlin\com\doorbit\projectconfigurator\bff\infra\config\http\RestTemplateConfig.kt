package com.doorbit.projectconfigurator.bff.infra.config.http

import com.doorbit.projectconfigurator.bff.core.domain.extension.setNonNull
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.*
import org.springframework.http.HttpHeaders
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.core.AbstractOAuth2Token
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.security.oauth2.jwt.JwtClaimNames
import org.springframework.web.client.RestTemplate
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import java.time.Duration


@Configuration
class RestTemplateConfig(
    @Lazy private val cFAuthorizationCookieInterceptor: CFAuthorizationCookieInterceptor,
) {

    @Bean
    fun restTemplateBuilder(): RestTemplateBuilder = RestTemplateBuilder()

    @Bean
    @Primary
    fun restTemplate(restTemplateBuilder: RestTemplateBuilder): RestTemplate = restTemplateBuilder
        .setConnectTimeout(Duration.ofMillis(500))
        .setReadTimeout(Duration.ofMillis(6000))
        .interceptors(createAuthInterceptor(), createDoorbitHeadersInterceptor())
        .build()

    @Bean
    @Qualifier(INTERNAL_REST_TEMPLATE)
    @Profile("!local-to-integ")
    fun internalRestTemplate(restTemplateBuilder: RestTemplateBuilder): RestTemplate = restTemplateBuilder
        .setConnectTimeout(Duration.ofMillis(500))
        .setReadTimeout(Duration.ofMillis(6000))
        .interceptors(createAuthInterceptor(), createDoorbitHeadersInterceptor())
        .build()

    @Bean
    @Qualifier(INTERNAL_REST_TEMPLATE)
    @Profile("local-to-integ")
    fun restTemplateWithCFAuthorizationCookie(restTemplateBuilder: RestTemplateBuilder): RestTemplate = restTemplateBuilder
        .setConnectTimeout(Duration.ofMillis(500))
        .setReadTimeout(Duration.ofMillis(6000))
        .interceptors(cFAuthorizationCookieInterceptor, createAuthInterceptor(), createDoorbitHeadersInterceptor())
        .build()

    @Bean
    @Qualifier(INTERNAL_REST_TEMPLATE_HIGH_TIMEOUT)
    @Profile("!local-to-integ")
    fun internalRestTemplateHighTimeout(restTemplateBuilder: RestTemplateBuilder): RestTemplate = restTemplateBuilder
        .setConnectTimeout(Duration.ofMillis(500))
        .setReadTimeout(Duration.ofMillis(45000))
        .interceptors(createAuthInterceptor(), createDoorbitHeadersInterceptor())
        .build()

    @Bean
    @Qualifier(INTERNAL_REST_TEMPLATE_HIGH_TIMEOUT)
    @Profile("local-to-integ")
    fun restTemplateWithCFAuthorizationCookieHighTimeout(restTemplateBuilder: RestTemplateBuilder): RestTemplate = restTemplateBuilder
        .setConnectTimeout(Duration.ofMillis(500))
        .setReadTimeout(Duration.ofMillis(45000))
        .interceptors(cFAuthorizationCookieInterceptor, createAuthInterceptor(), createDoorbitHeadersInterceptor())
        .build()


    private fun createDoorbitHeadersInterceptor() = ClientHttpRequestInterceptor { request, body, execution ->
        val requestAttributes = RequestContextHolder.getRequestAttributes()
        if (requestAttributes is ServletRequestAttributes) {
            val originatingRequest = requestAttributes.request
            request.headers.setNonNull(DoorbitHttpHeaders.RequestId, originatingRequest.getHeader(DoorbitHttpHeaders.RequestId))
            request.headers.setNonNull(DoorbitHttpHeaders.UserId, originatingRequest.getHeader(DoorbitHttpHeaders.UserId))
            request.headers.setNonNull(DoorbitHttpHeaders.ClientId, originatingRequest.getHeader(DoorbitHttpHeaders.ClientId))
            request.headers.setNonNull(DoorbitHttpHeaders.ClientVersion, originatingRequest.getHeader(DoorbitHttpHeaders.ClientVersion))
            request.headers.setNonNull(DoorbitHttpHeaders.ClientContext, originatingRequest.getHeader(DoorbitHttpHeaders.ClientContext))
            request.headers.setNonNull(DoorbitHttpHeaders.ListingId, originatingRequest.getHeader(DoorbitHttpHeaders.ListingId))
            request.headers.setNonNull(HttpHeaders.AUTHORIZATION, originatingRequest.getHeader(HttpHeaders.AUTHORIZATION))
            request.headers.setNonNull(DoorbitHttpHeaders.DemoMode, originatingRequest.getHeader(DoorbitHttpHeaders.DemoMode))
        }
        execution.execute(request, body)
    }

    private fun createAuthInterceptor(): ClientHttpRequestInterceptor = ClientHttpRequestInterceptor { request, body, execution ->
        val authentication: Authentication = SecurityContextHolder.getContext().authentication ?: return@ClientHttpRequestInterceptor execution.execute(request, body)

        val credentials = authentication.credentials
        if (credentials is AbstractOAuth2Token) {
            request.headers.setBearerAuth(credentials.tokenValue)
            if (credentials is Jwt) {
                request.headers.set(DoorbitHttpHeaders.UserId, credentials.claims[JwtClaimNames.SUB].toString())
            }

            return@ClientHttpRequestInterceptor execution.execute(request, body)
        }

        execution.execute(request, body)
    }

    companion object {
        const val INTERNAL_REST_TEMPLATE = "internalRestTemplate"
        const val INTERNAL_REST_TEMPLATE_HIGH_TIMEOUT = "internalRestTemplateHighTimeout"

    }
}
