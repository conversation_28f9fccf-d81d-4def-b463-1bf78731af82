package com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.productdatabase

object DbColumnConstants {

    // LEISTUNGSPOSITION
    val NAME = "Name"
    val UNIT_PRICE = "Einheitspreis"
    val UNIT = "Einheit"
    val MARGIN = "Marge"
    val VAT_RATE = "MwSt"
    val SECURITY_SURCHARGE = "Sicherheitsaufschlag"
    val DEFAULT_AMOUNT = "Standard-Menge"
    val SCHNELLKONFIGURATION = "Schnellkonfiguration"
    val CALCULATION_FORMULA = "Berechnungsformel"
    val DESCRIPTION = "Langtext"
    val STATUS = "Status"
    val DIN_NUMBER = "DIN Nummer"

    // LEISTUNG
    val U_VALUE = "u-Wert"
    val LEISTUNGSPOSITIONEN = "Leistungspositionen"
    val TYPE = "Typ"

    // VERGABEEINHEIT
    val MODULE = "Modul"
    val COMPONENT = "Komponente"
    val COMPONENTS = "Komponenten"

    // CONFIG
    val NUMBER = "Zahl"

}