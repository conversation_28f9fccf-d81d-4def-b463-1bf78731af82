<template>
  <v-form v-model="isNewConfigFormValid" @submit.prevent="createNewConfig">
    <v-card :title="t('domain.configurations.createNewConfig')">
      <div v-if="configCreationError" class="mx-6 my-4">
        <div class="text-subtitle-1 text-red">{{ t('domain.configurations.createNewError') }}</div>
        <div class="text-subtitle-2 text-red">
          <div>{{ configCreationError.name + ': ' + configCreationError.message }}</div>
        </div>
      </div>
      <v-card-text>
        <v-text-field
          v-model="newConfigLabel"
          :disabled="creationResponseLoading"
          :rules="[formRules.required, noDuplicateConfigurationNameFormRule]"
          density="compact"
          variant="outlined"
          rounded
          :placeholder="t('domain.configurations.configLabel')"
          autofocus
          tabindex="0"
        />
      </v-card-text>
      <v-select
        v-model="newConfigSelectedDbVersion"
        :disabled="creationResponseLoading"
        :rules="[formRules.required]"
        class="mx-6 config-creation__pre-select"
        :placeholder="t('domain.configurations.databaseVersion')"
        density="compact"
        variant="outlined"
        rounded
        :items="dbVersions"
        return-object
        :item-title="
          (item) =>
            item.version +
            ` (${new Date(item.createdAt).toLocaleDateString()} ${new Date(item.createdAt).toLocaleTimeString()})`
        "
        tabindex="0"
      />
      <div class="mx-8 mb-4">
        <div class="text-subtitle-1 mb-2">{{ t('domain.preSelection') }}</div>
        <div class="d-flex">
          <v-chip color="grey" :variant="preSelection === null ? 'flat' : 'tonal'" @click="preSelection = null">{{
            'Keine'
          }}</v-chip>
          <v-chip color="grey" :variant="preSelection === 'S' ? 'flat' : 'tonal'" @click="preSelection = 'S'">{{
            'Basic (S)'
          }}</v-chip>
          <v-chip color="grey" :variant="preSelection === 'M' ? 'flat' : 'tonal'" @click="preSelection = 'M'">{{
            'Power (M)'
          }}</v-chip>
          <v-chip color="grey" :variant="preSelection === 'L' ? 'flat' : 'tonal'" @click="preSelection = 'L'">{{
            'Premium (L)'
          }}</v-chip>
        </div>
      </div>
      <v-card-actions class="justify-end">
        <div v-if="creationResponseLoading" class="mx-6 d-flex justify-center align-center">
          <div class="text-uppercase">{{ t('common.pleaseWait') }}</div>
          <v-progress-circular indeterminate size="25" class="ml-4" />
        </div>
        <template v-else>
          <v-btn color="grey" tabindex="-1" @click="emits('finish')">
            {{ t('common.cancel') }}
          </v-btn>
          <v-btn type="submit" tabindex="0" :disabled="!isNewConfigFormValid" color="primary">
            {{ t('common.ok') }}
          </v-btn>
        </template>
      </v-card-actions>
    </v-card>
  </v-form>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import { FormRules, formRulesKey } from '@/utility/form-rules';
import { useI18n } from 'vue-i18n';
import {
  DatabaseVersion,
  SchnellkonfigurationType,
  useDCreateConfigMutation
} from '@/adapter/graphql/generated/graphql';
import { useProjectService } from '@/service/use-project-service';

const { t } = useI18n();
const {
  mutate: createNewConfiguration,
  loading: creationResponseLoading,
  error: configCreationError
} = useDCreateConfigMutation();
const { projectId, configurations, dbVersions } = useProjectService();
const emits = defineEmits<{ finish: [] }>();
const formRules = inject<FormRules>(formRulesKey)!;
const preSelection = ref<SchnellkonfigurationType | null>(null);
const isNewConfigFormValid = ref(false);
const newConfigLabel = ref('');
const newConfigSelectedDbVersion = ref<DatabaseVersion | null>(dbVersions.value[0] || null);

const noDuplicateConfigurationNameFormRule = (val: string) =>
  !configurations.value.map((config) => config.configurationName).includes(val) || 'Konfiguration existiert bereits';

async function createNewConfig() {
  const name = newConfigLabel.value + (preSelection.value ? ` (${preSelection.value})` : '');
  const creationResponse = await createNewConfiguration({
    projectId: projectId.value!,
    name,
    productDatabaseId: newConfigSelectedDbVersion.value!.databaseId,
    preSelection: preSelection.value
  });
  if (creationResponse?.data?.createConfiguration) {
    configurations.value = [
      ...configurations.value,
      {
        configurationName: name,
        configurationId: creationResponse.data.createConfiguration,
        databaseVersion: newConfigSelectedDbVersion.value!
      }
    ];
    emits('finish');
  }
}
</script>
<style lang="scss">
.config-creation__pre-select .v-selection-control__wrapper {
  display: none !important;
}
.v-selection-control-group {
  flex-direction: row;
  width: fit-content;
}
</style>
