@file:Suppress("UNCHECKED_CAST")

package com.doorbit.projectconfigurator.projectconfigurator.domain.extension

import com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.airtable.dto.RecordDto

fun RecordDto.arrayOfStringsOrNull(fieldName: String): List<String>? {
    return this.fields[fieldName] as? List<String>
}

fun RecordDto.doubleOrNull(fieldName: String): Double? {
    val doubleNumber = this.fields[fieldName] as? Double
    if (doubleNumber != null) {
        return doubleNumber
    }

    return (this.fields[fieldName] as? Int)?.toDouble()
}

fun RecordDto.stringOrNull(fieldName: String): String? {
    return this.fields[fieldName] as? String
}