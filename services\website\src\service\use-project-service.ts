import { computed, inject, InjectionKey, provide, ref } from 'vue';
import {
  ConfigurationTable,
  DatabaseVersion,
  DisplayContext,
  useDGetProjectQuery
} from '@/adapter/graphql/generated/graphql';
import { useRoute } from 'vue-router';

export interface ConfigurationEntity
  extends Pick<ConfigurationTable, 'configurationName' | 'configurationId' | 'databaseVersion'> {}

export type ProjectService = ReturnType<typeof initializeProjectService>;

const projectServiceKey = Symbol('projectService') as InjectionKey<ProjectService>;
const LISTING_QUERY_KEY = 'l';
const DISPLAY_CONTEXT_QUERY_KEY = 'c';

export function initializeProjectService(provideFn: typeof provide) {
  const route = useRoute();
  const listingIdFromUrl = route.query[LISTING_QUERY_KEY];
  const displayContextFromUrl = route.query[DISPLAY_CONTEXT_QUERY_KEY];
  const listingId = ref<string>(typeof listingIdFromUrl === 'string' ? listingIdFromUrl : '');
  const displayContext = ref<DisplayContext>(
    displayContextFromUrl === 'BACKOFFICE' || displayContextFromUrl === 'END_CUSTOMER'
      ? displayContextFromUrl
      : 'BACKOFFICE'
  );
  const projectId = ref<string | null>(null);
  const configurations = ref<ConfigurationEntity[]>([]);
  const selectedConfiguration = ref<ConfigurationEntity | null>(null);
  const dbVersions = ref<DatabaseVersion[]>([]);

  const isDev = computed(() => import.meta.env.DEV);

  const {
    error: getProjectError,
    loading: isProjectLoading,
    onResult,
    restart: restartGetProject
  } = useDGetProjectQuery({ listingId: listingId.value, displayContext: displayContext.value });
  onResult((result) => {
    if (result.data.projectByListingId) {
      projectId.value = result.data.projectByListingId.id;
      listingId.value = result.data.projectByListingId.listingId;
      configurations.value = result.data.projectByListingId.configurations;
      dbVersions.value = result.data.databaseVersions;
    }
  });

  const projectService = {
    listingId,
    projectId,
    configurations,
    getProjectError,
    isProjectLoading,
    restartGetProject,
    selectedConfiguration,
    dbVersions,
    displayContext,
    isDev
  };
  provideFn<ProjectService>(projectServiceKey, projectService);
  return projectService;
}

export function useProjectService(): ProjectService {
  return inject<ProjectService>(projectServiceKey)!;
}
