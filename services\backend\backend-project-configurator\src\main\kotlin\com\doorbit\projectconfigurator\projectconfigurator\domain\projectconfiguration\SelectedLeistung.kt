package com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration

import com.doorbit.projectconfigurator.projectconfigurator.domain.LeistungId
import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.toMoney
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.DbLeistungsposition
import java.math.BigDecimal

typealias LeistungsPositionId = String

data class SelectedLeistung(
    val id: LeistungId,
    var isSelected: Boolean = true,
    var execution: LeistungExecutionType = LeistungExecutionType.CONTRACTOR,
    val configuredLeistungsPositionen: MutableSet<ConfiguredLeistungsPosition> = mutableSetOf()
) {
    @Suppress("IfThenToElvis")
    fun configureLeistungsPosition(leistungsPositionId: LeistungsPositionId, unitPrice: BigDecimal?, vatRatePercent: BigDecimal?, amount: BigDecimal?, margin: BigDecimal?, securitySurcharge: BigDecimal?, notes: String?, dbLeistungsPosition: DbLeistungsposition) : ConfiguredLeistungsPosition {
        var clp = findConfiguredLeistungsPosition(leistungsPositionId)
        clp = if (clp != null) {
            clp.change(unitPrice, vatRatePercent, amount, margin, securitySurcharge, notes)
        } else newConfiguredLeistungsPosition(leistungsPositionId, unitPrice, vatRatePercent, amount, margin, securitySurcharge, notes, dbLeistungsPosition)

        if (clp in configuredLeistungsPositionen) {
            // Replace the old one
            configuredLeistungsPositionen.remove(clp)
        }

        configuredLeistungsPositionen.add(clp)
        return clp
    }

    private fun newConfiguredLeistungsPosition(
        leistungsPositionId: LeistungsPositionId,
        unitPrice: BigDecimal?,
        vatRatePercent: BigDecimal?,
        amount: BigDecimal?,
        margin: BigDecimal?,
        securitySurcharge: BigDecimal?,
        notes: String?,
        dbLeistungsPosition: DbLeistungsposition,
    ): ConfiguredLeistungsPosition {
        val unitPriceToUse = unitPrice ?: dbLeistungsPosition.unitPrice
        val vatRateToUse = vatRatePercent ?: dbLeistungsPosition.vatRatePercent
        val marginToUse = margin ?: dbLeistungsPosition.marginPercent
        val securitySurchargeToUse = securitySurcharge ?: dbLeistungsPosition.securitySurchargePercent
        val amountToUse = amount ?: dbLeistungsPosition.defaultAmount ?: BigDecimal.ONE
        return ConfiguredLeistungsPosition(
            leistungsPositionId,
            unitPriceToUse.toMoney(),
            vatRateToUse.toMoney(),
            marginToUse.toMoney(),
            securitySurchargeToUse.toMoney(),
            amountToUse.toMoney(),
            notes
        )
    }

    fun changeSelectionState(selection: Boolean) : SelectedLeistung {
        this.isSelected = selection
        return this
    }

    private fun findConfiguredLeistungsPosition(leistungsPositionId: LeistungsPositionId): ConfiguredLeistungsPosition? {
        return configuredLeistungsPositionen.find { it.id == leistungsPositionId }
    }

    fun calculate() {
        if (!isSelected || execution == LeistungExecutionType.CLIENT) {
            configuredLeistungsPositionen.forEach(ConfiguredLeistungsPosition::setCalculationToZero)
            return
        }

        configuredLeistungsPositionen.forEach { it.calculate() }
    }

    fun totalNetCosts(): BigDecimal {
        return configuredLeistungsPositionen.sumOf { it.calculation.netCosts }.toMoney()
    }

    fun totalMargin(): BigDecimal {
        return configuredLeistungsPositionen.sumOf { it.calculation.margin }.toMoney()
    }

    fun totalVatAmount(): BigDecimal {
        return configuredLeistungsPositionen.sumOf { it.calculation.vatAmount }.toMoney()
    }

    fun totalSecuritySurcharge(): BigDecimal {
        return configuredLeistungsPositionen.sumOf { it.calculation.securitySurcharge }.toMoney()
    }

    fun totalOfferPriceGross(): BigDecimal {
        return configuredLeistungsPositionen.sumOf { it.calculation.offerPriceGross }.toMoney()
    }

    fun configuredLeistungsPosition(leistungsPositionId: LeistungsPositionId): ConfiguredLeistungsPosition? {
        return configuredLeistungsPositionen.find { it.id == leistungsPositionId }
    }

    enum class LeistungExecutionType {
        CLIENT,
        CONTRACTOR
    }

}
