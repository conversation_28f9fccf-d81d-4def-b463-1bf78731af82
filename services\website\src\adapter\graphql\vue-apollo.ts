import { Apollo<PERSON><PERSON>, ApolloLink, createHttpLink, DefaultOptions, InMemoryCache, split } from '@apollo/client/core';
import { createApolloProvider } from '@vue/apollo-option';
import { provideApolloClient } from '@vue/apollo-composable';
import { buildServerUrl, buildServerWsUrl } from '@/utility/server';
import { AUTH_SERVICE } from '@/service/auth/AuthService';
import { v4 as uuidv4 } from 'uuid';
import { BUILD_VERSION, CLIENT_SECRET, IS_DEVELOPMENT } from '@/utility/environment';
import { getMainDefinition } from '@apollo/client/utilities';
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { createClient } from 'graphql-ws';

const GRAPHQL_HOST = buildServerUrl('/graphql');
const GRAPHQL_WS_HOST = buildServerWsUrl('/subscriptions');

if (IS_DEVELOPMENT) {
  console.log('GraphQL Host = ', GRAPHQL_HOST, 'GraphQL WS Host = ', GRAPHQL_WS_HOST);
}

const httpLink = createHttpLink({
  uri: GRAPHQL_HOST
});

export function constructHttpHeaders(): Record<string, string | undefined> {
  const headers: Record<string, string> = {
    'X-Client-Version': BUILD_VERSION,
    'X-Client-Id': 'Project Configurator',
    'X-Client-Context': location.pathname,
    'X-Request-Id': uuidv4(),
    'X-Request-Payload': CLIENT_SECRET
  };
  if (AUTH_SERVICE.accessToken.value !== null) {
    headers['Authorization'] = `Bearer ${AUTH_SERVICE.accessToken.value}`;
  }
  return headers;
}

const wsLink = new GraphQLWsLink(
  createClient({
    url: GRAPHQL_WS_HOST,
    lazy: true,
    connectionParams: () => {
      return {
        headers: constructHttpHeaders()
      };
    }
  })
);

const splitLink = split(
  // split based on operation type
  ({ query }) => {
    const definition = getMainDefinition(query);
    return definition.kind === 'OperationDefinition' && definition.operation === 'subscription';
  },
  wsLink,
  httpLink
);

const headersLink = new ApolloLink((operation, forward) => {
  const headers = constructHttpHeaders();

  operation.setContext({ headers });

  return forward(operation);
});

//cache deaktivieren sorgt für aktuelle daten nach mutations
const defaultOptions = {
  watchQuery: {
    fetchPolicy: 'no-cache'
    //errorPolicy: 'ignore', //TODO prüfen
  },
  query: {
    fetchPolicy: 'no-cache'
    //errorPolicy: 'all', //TODO prüfen
  },
  subscription: {
    fetchPolicy: 'no-cache'
  }
} as DefaultOptions;

const apolloClient = new ApolloClient({
  link: ApolloLink.from([headersLink, splitLink]),
  cache: new InMemoryCache({
    addTypename: false
  }),
  connectToDevTools: IS_DEVELOPMENT,
  defaultOptions
});

provideApolloClient(apolloClient);

export default createApolloProvider({
  defaultClient: apolloClient
});
