package com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase

import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.LeistungCreateDto
import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.LeistungsPositionCreateDto
import com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.productdatabase.DbColumnConstants.UNIT

object DbCustomTableItemMapper {

    fun map(payload: LeistungCreateDto): DbLeistung {
        return DbLeistung(
            recordId = "custom_${System.currentTimeMillis()}",
            name = payload.displayName,
            type = LeistungType.fromString(payload.leistungType.value),
            leistungsPositionen = mutableListOf(),
            otherFields = mapOf(
                DbLeistungFields.OFFER_PRICE.value to payload.offerPrice
            ),
            customCreatedLeistung = DbCustomLeistungInfo(
                globallyAvailable = payload.saveAsGlobal
            ),
            schnellkonfiguration = emptyList()
        )
    }

    fun mapLeistungsPosition(payload: LeistungsPositionCreateDto): DbLeistungsposition {
        return DbLeistungsposition(
            recordId = "custom_${System.currentTimeMillis()}",
            name = payload.displayName,
            unitPrice = payload.unitPrice,
            vatRatePercent = payload.vatRatePercent,
            marginPercent = payload.marginPercent,
            securitySurchargePercent = payload.securitySurchargePercent,
            otherFields = mapOf(UNIT to payload.unit),
            isCustomCreatedLeistungsPosition = true
        )
    }

}
