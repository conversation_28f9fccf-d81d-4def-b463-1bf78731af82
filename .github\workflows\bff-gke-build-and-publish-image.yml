#@formatter:off
name: bff-gke-build-and-publish-image
on:
  workflow_dispatch:
    inputs:
      branch:
        description: "The branch to use"
        required: false
        default: "main"
  push:
    branches: [ main ]
    paths:
      - services/backend/bff/**
      - services/backend/backend-project-configurator/**
env:
  WORKING_DIR: services/backend/
  GRAPHQL_SCHEMA_LOCATION: api/graphql/schema/
  JDK: '21'
  NODE: '21'
  MVN_ARGS: -B -q

  DOCKER_IMAGE_NAME: projectconfigurator-bff-microservice

jobs:
  build:
    name: build-and-publish
    runs-on: ubuntu-latest
    steps:
      - name: Store short sha in env
        run: echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-7`" >> $GITHUB_ENV

      - name: Create version tag
        run: echo "VERSION_TAG=gh${{ github.run_number }}-${{  env.SHORT_SHA  }}" >> $GITHUB_ENV

      - name: "Checkout code"
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: "Set up JDK"
        uses: actions/setup-java@v4
        with:
          java-version: ${{ env.JDK }}
          distribution: 'temurin'

      - name: "Use Maven packages cache"
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository/
          key: ${{ runner.os }}-mvn-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-mvn

      - name: Build
        working-directory: ${{ env.WORKING_DIR }}
        run: mvn ${{ env.MVN_ARGS }} clean verify

      - name: Set docker image env var
        run: |
          echo "DOCKER_IMAGE=${{ vars.ARTIFACTORY_DOCKER_URL }}/${DOCKER_IMAGE_NAME}:${GITHUB_REF##*/}.${{ env.VERSION_TAG }}" >> $GITHUB_ENV

      - name: Build docker image
        run: docker build -t $DOCKER_IMAGE -f build-tools/Dockerfile-Bff --build-arg target=${{ env.WORKING_DIR }}bff/target --build-arg graphqlSchema=${{ env.GRAPHQL_SCHEMA_LOCATION }} .

      - name: Authenticate with GCP project that hosts the docker repo
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_CICD_PROJECT_ARTIFACTORY_CREDENTIALS }}

      - name: Set up gcloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Push docker image
        run: |
          gcloud auth configure-docker ${{ vars.ARTIFACTORY_HOST }}

          docker push $DOCKER_IMAGE

      - name: Notify GitOps Repository via WebHook
        uses: peter-evans/repository-dispatch@v3
        with:
          event-type: docker-image-pushed
          token: ${{ secrets.PAT_GITOPS }}
          repository: Doorbit/portal-gitops
          client-payload: '{"stage": "integ", "deployment": "${{ env.DOCKER_IMAGE_NAME }}", "new_version": "${{ env.VERSION_TAG }}"}'

      - name: Get Commit Message
        run: echo "COMMIT_MESSAGE=${{ env.DOCKER_IMAGE_NAME }}" >> $GITHUB_ENV
        if: always()

      - name: Google chat notification
        uses: SimonScholz/google-chat-action@main
        with:
          title: ${{ env.COMMIT_MESSAGE }}
          webhookUrl: ${{ secrets.GOOGLE_CHAT_WEBHOOK_URL }}
          jobStatus: ${{ job.status }}
          imageUrl: 'https://fonts.gstatic.com/s/e/notoemoji/15.0/1f916/32.png'
          imageType: SQUARE
        if: always()
