package com.doorbit.projectconfigurator.bff.infra.api.dto

import com.doorbit.projectconfigurator.bff.core.domain.extension.rounded
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.DbLeistung
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.DbLeistungFields
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.SelectedLeistung
import kotlin.random.Random

object LeistungDetailsMapper {

    fun toLeistungDetailsDto(dbLeistung: DbLeistung, selectedLeistung: SelectedLeistung?, displayContext: DisplayContext?): LeistungDetails {

        val mapper = ConfigurationTableMapper(displayContext ?: DisplayContext.BACKOFFICE)

        return LeistungDetails(
            id = dbLeistung.recordId,
            displayName = dbLeistung.name,
            longDisplayName = dbLeistung.otherFields[DbLeistungFields.NAME_LONG.value] as? String ?: longDisplayNameFallback(),
            longDescription = dbLeistung.otherFields[DbLeistungFields.DESCRIPTION.value] as? String ?: longDescriptionFallback(),
            isSelected = selectedLeistung?.isSelected ?: false,
            type = dbLeistung.type?.let { LeistungType.valueOf(it.name) } ?: LeistungType.ALTERNATIVE,
            leistungsPositionen = dbLeistung.leistungsPositionen.map { mapper.mapLeistungsposition(selectedLeistung?.configuredLeistungsPosition(it.recordId), it) },
            imageUrls = extractPhotos(dbLeistung),
            isCustomCreated = if (displayContext == DisplayContext.END_CUSTOMER) null else dbLeistung.customCreatedLeistung != null,
            table = mapLeistungDetailTable(dbLeistung, selectedLeistung)
        )
    }

    @Suppress("UNCHECKED_CAST")
    private fun extractPhotos(dbLeistung: DbLeistung): List<String> {
        val photosDbObject = dbLeistung.otherFields[DbLeistungFields.PHOTOS.value] as? List<Map<*,*>>?

        return photosDbObject?.mapNotNull {
            it["url"]?.toString()
        } ?: emptyList()
    }

    private fun longDescriptionFallback(): String {
        return "A long description that is really long and detailed." +
                "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum."
    }

    private fun longDisplayNameFallback(): String {
        return "A long display name"
    }

    private fun mapLeistungDetailTable(dbLeistung: DbLeistung, selectedLeistung: SelectedLeistung?): LeistungDetailTable {
        return LeistungDetailTable(
            eligible = true,
            execution = selectedLeistung?.execution?.let { ExecutionType.valueOf(it.name) },
            factSheetUrl = dbLeistung.otherFields[DbLeistungFields.FACTSHEET_URL.value] as? String ?: "https://cdn.cloud.grohe.com/Web/local_PDF/corporate/180302_Factsheet_Hemer_EN/original/180302_Factsheet_Hemer_EN.pdf",
            manufacturer = dbLeistung.otherFields[DbLeistungFields.MANUFACTURER.value] as? String ?: "Dummy Manufacturer",
            uValue = dbLeistung.otherFields[DbLeistungFields.U_VALUE.value] as? Double ?: Random.nextDouble(0.1, 3.0).rounded(2),
            offerPriceGross = selectedLeistung?.totalOfferPriceGross()?.toDouble(),
        )
    }

}
