<template>
  <v-dialog max-width="700">
    <template #activator="{ props: activatorProps }">
      <v-btn flat color="black" variant="tonal" :prepend-icon="mdiPlus" v-bind="activatorProps">{{
        t('common.new')
      }}</v-btn>
    </template>
    <template #default="{ isActive }">
      <v-card>
        <v-card-title class="text-pre-wrap">{{ t('domain.leistungsposition.newLeistungsposition') }}</v-card-title>
        <v-form v-model="valid" @submit.prevent="onSubmit">
          <v-card-text>
            <v-row>
              <v-col cols="6">
                <v-textarea
                  v-model.trim="displayName"
                  rows="2"
                  rounded
                  density="compact"
                  variant="outlined"
                  :label="t('domain.displayName')"
                  :rules="[formRules.required]"
                  autofocus
                  tabindex="0"
                />
                <v-text-field
                  v-model.number="amount"
                  :on-click:clear="() => (amount = 0)"
                  rounded
                  density="compact"
                  variant="outlined"
                  type="number"
                  min="0"
                  :label="t('domain.amount')"
                  tabindex="0"
                />
                <v-combobox
                  v-model.trim="unit"
                  hide-no-data
                  :items="defaults!.existingUnits"
                  :on-click:clear="() => (unit = '')"
                  rounded
                  :rules="[formRules.required, (val) => val.length <= 10 || 'Max 10 Zeichen']"
                  density="compact"
                  variant="outlined"
                  :label="t('domain.leistungsposition.unitCreateOrSelect')"
                  tabindex="0"
                  @update:model-value="unit = $event"
                />
                <v-text-field
                  v-model.number="unitPrice"
                  :on-click:clear="() => (unitPrice = 0)"
                  rounded
                  density="compact"
                  variant="outlined"
                  type="number"
                  min="0"
                  :label="t('domain.unitPrice')"
                  prefix="€"
                  tabindex="0"
                />
                <v-text-field
                  v-model.number="marginPercent"
                  :on-click:clear="() => (marginPercent = defaults!.marginPercent)"
                  rounded
                  density="compact"
                  variant="outlined"
                  type="number"
                  min="0"
                  max="100"
                  :label="t('domain.margin')"
                  prefix="%"
                  tabindex="0"
                />
                <v-text-field
                  v-model.number="securitySurchargePercent"
                  :on-click:clear="() => (securitySurchargePercent = defaults!.securitySurchargePercent)"
                  rounded
                  density="compact"
                  variant="outlined"
                  type="number"
                  min="0"
                  max="100"
                  :label="t('domain.securitySurcharge')"
                  prefix="%"
                  tabindex="0"
                />
                <v-text-field
                  v-model.number="vatRatePercent"
                  :on-click:clear="() => (vatRatePercent = 0)"
                  rounded
                  density="compact"
                  variant="outlined"
                  type="number"
                  min="0"
                  max="100"
                  :label="t('domain.vatAmount')"
                  prefix="%"
                  tabindex="0"
                />
                <v-switch
                  v-model="globallyAvailable"
                  class="ml-2"
                  color="success"
                  density="compact"
                  hide-details
                  :label="
                    globallyAvailable
                      ? t('domain.globallyAvailable.globallyAvailable')
                      : t('domain.globallyAvailable.notGloballyAvailable')
                  "
                  tabindex="0"
                />
              </v-col>
              <v-col cols="6">
                <d-massedaten @selected="amount = $event ?? amount" />
              </v-col>
            </v-row>
          </v-card-text>
          <v-textarea
            v-model.trim="notes"
            :on-click:clear="() => (notes = '')"
            rounded
            class="mx-4"
            rows="2"
            density="compact"
            variant="outlined"
            :label="t('domain.notes')"
            tabindex="0"
          />
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="grey" :text="t('common.cancel')" tabindex="-1" @click="isActive.value = false"></v-btn>
            <v-btn
              variant="tonal"
              type="submit"
              :disabled="!valid"
              :text="t('common.save')"
              tabindex="0"
              @click="isActive.value = false"
            ></v-btn>
          </v-card-actions>
        </v-form>
      </v-card>
    </template>
  </v-dialog>
</template>

<script lang="ts" setup>
import { mdiPlus } from '@mdi/js';
import { inject, ref } from 'vue';
import { FormRules, formRulesKey } from '@/utility/form-rules';
import { LeistungspositionCreationForm } from './LeistungspositionCreationForm';
import { useLeistungspositionDefaultsService } from '@/service/use-leistungsposition-defaults-service';
import { roundCurrency, roundPercentage } from '@/utility/rounding';
import { useDGetLeistungspositionDefaultsQuery } from '@/adapter/graphql/generated/graphql';
import { useProjectService } from '@/service/use-project-service';
import { useI18n } from 'vue-i18n';
import DMassedaten from '@/components/content/d-massedaten.vue';

const { t } = useI18n();
const formRules = inject<FormRules>(formRulesKey)!;
const { defaults } = useLeistungspositionDefaultsService();
const { selectedConfiguration } = useProjectService();
const emits = defineEmits<{ submit: [form: LeistungspositionCreationForm] }>();
const valid = ref(false);
const displayName = ref<string>('');
const unit = ref<string>('');
const notes = ref<string>('');
const globallyAvailable = ref<boolean>(false);
const marginPercent = ref<number>(defaults.value!.marginPercent * 100);
const securitySurchargePercent = ref<number>(defaults.value!.securitySurchargePercent * 100);
const unitPrice = ref<number>(0);
const vatRatePercent = ref<number>(defaults.value!.vatRatePercent * 100);
const amount = ref<number>(0);

function onSubmit() {
  emits('submit', {
    displayName: displayName.value,
    globallyAvailable: globallyAvailable.value,
    marginPercent: roundPercentage(marginPercent.value / 100),
    securitySurchargePercent: roundPercentage(securitySurchargePercent.value / 100),
    unitPrice: roundCurrency(unitPrice.value),
    vatRatePercent: roundPercentage(vatRatePercent.value / 100),
    amount: amount.value,
    unit: unit.value,
    notes: notes.value
  });
  if (!defaults.value!.existingUnits.includes(unit.value)) {
    useDGetLeistungspositionDefaultsQuery({
      configurationId: selectedConfiguration.value!.configurationId
    }).onResult((result) => {
      defaults.value = result.data.leistungsPositionDefaults;
    });
  }
  // TODO: move backend handling here and submit only on success otherwise retry logic within dialog
  valid.value = false;
  displayName.value = '';
  unit.value = '';
  notes.value = '';
  globallyAvailable.value = true;
  marginPercent.value = defaults.value!.marginPercent * 100;
  securitySurchargePercent.value = defaults.value!.securitySurchargePercent * 100;
  unitPrice.value = 0;
  amount.value = 0;
  vatRatePercent.value = defaults.value!.vatRatePercent * 100;
}
</script>

<style scoped></style>
