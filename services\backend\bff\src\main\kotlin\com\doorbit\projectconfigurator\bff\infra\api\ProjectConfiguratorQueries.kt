package com.doorbit.projectconfigurator.bff.infra.api

import com.doorbit.projectconfigurator.bff.core.domain.extension.WithLogger
import com.doorbit.projectconfigurator.bff.core.domain.extension.d
import com.doorbit.projectconfigurator.bff.core.domain.extension.i
import com.doorbit.projectconfigurator.bff.infra.adapter.ProjectConfiguratorApiAdapter
import com.doorbit.projectconfigurator.bff.infra.api.dto.*
import com.doorbit.projectconfigurator.projectconfigurator.domain.LeistungId
import org.bson.types.ObjectId
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.QueryMapping
import org.springframework.stereotype.Controller
import java.io.File

const val LISTING_ID = "listingId"

@Controller
class ProjectConfiguratorQueries(
    private val projectConfiguratorApiAdapter: ProjectConfiguratorApiAdapter,
    private val configurationTableService: ConfigurationTableService,
) {

    @EventListener(ApplicationReadyEvent::class)
    fun initData() {
        if (projectConfiguratorApiAdapter.getDatabaseVersions().isEmpty()) {
            println("##########################")
            println("##########################")
            println("Importiere ProductDatabase und erzeuge TestConfig")
            println("##########################")
            println("##########################")
            // Not initialized
            projectConfiguratorApiAdapter.importProductDatabase("initialVersion")
            val dbId = projectConfiguratorApiAdapter.getDatabaseVersions().first().id
            val project = projectConfiguratorApiAdapter.createProject(LISTING_ID)
            val configId = projectConfiguratorApiAdapter.createConfiguration(project.id.toHexString(), dbId, "test configuration", null)

            File("devsetup/data/.test-configuration-id").bufferedWriter().use { out ->
                out.write(configId.toString())
                out.newLine()
            }

            println("##########################")
            println("##########################")
            println("##########################")
            println("TestConfig mit Id $configId erstellt.")
            println("##########################")
            println("##########################")
            println("##########################")
        }
    }

    @QueryMapping
    fun configurationTable(@Argument configurationId: String, @Argument displayContext: DisplayContext): ConfigurationTable {
        LOGGER.i { "configurationTable($configurationId, $displayContext)"}
        return configurationTableService.findConfigurationTable(configurationId, displayContext)!!
    }

    @QueryMapping
    fun databaseVersions(): List<DatabaseVersion> {
        LOGGER.i { "databaseVersions()" }
        return projectConfiguratorApiAdapter.getDatabaseVersions()
            .map { DatabaseVersion(
                createdAt = it.importedAt.toString(),
                databaseId = it.id.toHexString(),
                version = it.versionName
            )}
    }

    @QueryMapping
    fun leistungDetails(
        @Argument configurationId: String,
        @Argument leistungId: LeistungId,
        @Argument displayContext: DisplayContext?
    ): LeistungDetails {
        LOGGER.d { "leistungDetails($configurationId, $leistungId)"}
        val configId = ObjectId(configurationId)
        val dbLeistung = projectConfiguratorApiAdapter.getProductDatabase(configId).leistung(leistungId)
        val selectedLeistung = projectConfiguratorApiAdapter.getProjectConfiguration(configId).leistungen.selectedLeistung(leistungId, false)
        return LeistungDetailsMapper.toLeistungDetailsDto(dbLeistung = dbLeistung, selectedLeistung = selectedLeistung, displayContext)
    }

    @QueryMapping
    fun leistungsPositionDefaults(@Argument configurationId: String) : LeistungsPositionDefaults {
        LOGGER.d { "leistungsPositionDefaults($configurationId)"}
        val configId = ObjectId(configurationId)
        val db = projectConfiguratorApiAdapter.getProductDatabase(configId)
        return LeistungsPositionDefaultsMapper.toLeistungsPositionDefaultsDto(db)
    }

    @QueryMapping
    fun massedaten(@Argument projectId: String): List<Massedatum> {
        LOGGER.d { "massedaten($projectId)"}
        val massedaten = projectConfiguratorApiAdapter.getMassedaten(ObjectId(projectId)) ?: return emptyList()
        return MassedatenMapper.toDto(massedaten)
    }

    companion object : WithLogger()

}
