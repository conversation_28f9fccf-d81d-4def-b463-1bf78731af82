package com.doorbit.projectconfigurator.projectconfigurator.repository

import org.springframework.core.env.Environment

abstract class RepositoryBase(
    private val className: String,
    environment: Environment,
) {

    protected val collectionName: String = determineCollectionName(environment)

    private fun determineCollectionName(environment: Environment): String {
        return if (environment.activeProfiles.contains("integ")) {
            "$className-integ"
        } else {
            className
        }
    }

}