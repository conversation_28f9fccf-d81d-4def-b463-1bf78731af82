// …/node_modules/vite/types/importMeta.d.ts#ImportMetaEnv

const env = import.meta.env;

export const BUILD_VERSION = env.VITE_BUILD_GIT_SHORT_SHA;
export const IS_DEVELOPMENT = env.VITE_IS_DEVELOPMENT === 'true';
export const CLIENT_SECRET = env.VITE_CLIENT_SECRET ?? '';
export const BFF_PORT = env.VITE_BFF_PORT ?? '';
export const BFF_WS_HOST = env.VITE_BFF_WS_HOST ?? '';

console.log('Environment: ', env.MODE);

export const IS_SAFARI = /^((?!chrome|android).)*safari/i.test(window.navigator.userAgent);
