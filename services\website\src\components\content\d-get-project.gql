query dGetProject($listingId: String!, $displayContext: DisplayContext!) {
  databaseVersions {
    databaseId
    version
    createdAt
  }
  projectByListingId(listingId: $listingId, displayContext: $displayContext) {
    id
    listingId
    configurations {
      configurationId
      configurationName
      databaseVersion {
        databaseId
        version
        createdAt
      }
    }
  }
}
