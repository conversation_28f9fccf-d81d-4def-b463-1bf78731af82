<template>
  <d-loading-cendered v-if="isConfigurationTableLoading" />
  <template v-else>
    <template v-if="!configurationTable">
      <d-apollo-error
        v-if="configurationTableError"
        fullscreen
        :error="configurationTableError"
        :reload="loadConfigurationTable"
      />
      <d-generic-error v-else :message="t('errorViews.projectDataNotLoaded')" fullscreen />
    </template>
  </template>
</template>

<script setup lang="ts">
import DApolloError from '@/components/ui/d-apollo-error.vue';
import DLoadingCendered from '@/components/layout/d-layout-desktop.vue';
import DGenericError from '@/components/ui/d-generic-error.vue';
import { useProjectService } from '@/service/use-project-service';
import { watch } from 'vue';
import { useConfigurationTableService } from '@/service/use-configuration-table-service';
import {
  DConfigTableFragment,
  useDGetConfiguratorDataLazyQuery,
  useDGetLeistungspositionDefaultsQuery
} from '@/adapter/graphql/generated/graphql';
import { useLeistungspositionDefaultsService } from '@/service/use-leistungsposition-defaults-service';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { selectedConfiguration, displayContext } = useProjectService();
const { defaults } = useLeistungspositionDefaultsService();
const { configurationTable, setConfigTable } = useConfigurationTableService();
const {
  loading: isConfigurationTableLoading,
  error: configurationTableError,
  load: loadConfigurationTable,
  variables: configurationTableVariables,
  onResult: onConfigTableLoaded
} = useDGetConfiguratorDataLazyQuery();

onConfigTableLoaded((res) => {
  setConfigTable(res.data.configurationTable as DConfigTableFragment);
});

watch(
  [selectedConfiguration, displayContext],
  async ([selectedConfigurationNew, displayContextNew]) => {
    if (selectedConfigurationNew) {
      configurationTableVariables.value = {
        configurationId: selectedConfigurationNew.configurationId,
        displayContext: displayContextNew
      };
      await loadConfigurationTable();

      if (displayContextNew === 'BACKOFFICE') {
        useDGetLeistungspositionDefaultsQuery({ configurationId: selectedConfigurationNew.configurationId }).onResult(
          (result) => {
            defaults.value = result.data.leistungsPositionDefaults;
          }
        );
      }
    }
  },
  { immediate: true }
);
</script>

<style scoped></style>
