package com.doorbit.projectconfigurator.bff.infra.adapter

import com.doorbit.projectconfigurator.bff.infra.api.dto.LeistungInput
import com.doorbit.projectconfigurator.bff.infra.api.dto.ExecutionType
import com.doorbit.projectconfigurator.bff.infra.api.dto.LeistungsPositionInput
import com.doorbit.projectconfigurator.bff.infra.api.dto.SchnellkonfigurationType
import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.ProjectConfiguratorApi
import com.doorbit.projectconfigurator.projectconfigurator.domain.LeistungId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProductDatabaseId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProjectConfigurationId
import com.doorbit.projectconfigurator.projectconfigurator.domain.massedaten.Massedatum
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.ProductDatabase
import com.doorbit.projectconfigurator.projectconfigurator.domain.project.Project
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.LeistungsPositionId
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.ProjectConfiguration
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.SelectedLeistung
import org.bson.types.ObjectId
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class ProjectConfiguratorApiAdapter(
    private val projectConfiguratorApi: ProjectConfiguratorApi
) {
    fun importProductDatabase(versionName: String): ProductDatabaseId {
        return projectConfiguratorApi.importProductDatabase(versionName)
    }

    fun createConfiguration(projectId: String, productDatabaseId: ProductDatabaseId, name: String, schnellKonfiguration: SchnellkonfigurationType?): ProjectConfigurationId {
        val schnellkonfigurationType = schnellKonfiguration?.let { com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.SchnellkonfigurationType.valueOf(it.name) }
        return projectConfiguratorApi.createConfiguration(projectId, productDatabaseId, name, schnellkonfigurationType)
    }

    fun getDatabaseVersions(): List<ProductDatabase> {
        return projectConfiguratorApi.getDatabaseVersions()
    }

    fun getProductDatabase(configurationId: ProjectConfigurationId): ProductDatabase {
        return projectConfiguratorApi.loadProductDatabase(configurationId)
    }

    fun getProjectConfiguration(configurationId: ProjectConfigurationId): ProjectConfiguration {
        return projectConfiguratorApi.getProjectConfiguration(configurationId)
    }

    fun selectLeistung(configurationId: ProjectConfigurationId, leistungId: String, selection: Boolean) {
        projectConfiguratorApi.selectLeistung(configurationId, leistungId, selection)
    }

    fun selectLeistungsPosition(configurationId: ProjectConfigurationId, leistungId: LeistungId, leistungsPositionId: LeistungsPositionId, selection: Boolean) {
        projectConfiguratorApi.selectLeistungsPosition(configurationId, leistungId, leistungsPositionId, selection)
    }

    fun changeExecution(configurationId: ProjectConfigurationId, leistungId: LeistungId, executionType: ExecutionType) {
        projectConfiguratorApi.changeExecution(configurationId, leistungId, SelectedLeistung.LeistungExecutionType.valueOf(executionType.name))
    }

    fun createLeistung(configurationId: ProjectConfigurationId, komponenteId: String, payload: LeistungInput) {
        val dto = BackendProjectConfiguratorMapper.toCustomLeistungCreateDto(payload)
        projectConfiguratorApi.createLeistung(configurationId, komponenteId, dto)
    }

    fun createLeistungsPosition(configurationId: ProjectConfigurationId, leistungId: LeistungId, payload: LeistungsPositionInput) {
        val dto = BackendProjectConfiguratorMapper.toCustomLeistungsPositionCreateDto(payload)
        projectConfiguratorApi.createLeistungsPosition(configurationId, leistungId, dto)
    }

    fun configureLeistungsPosition(configurationId: ProjectConfigurationId, leistungId: LeistungId, leistungsPositionId: LeistungsPositionId, unitPrice: BigDecimal?, vatRatePercent: BigDecimal?, amount: BigDecimal?, margin: BigDecimal?, notes: String?, securitySurcharge: BigDecimal?) {
        projectConfiguratorApi.configureLeistungsPosition(configurationId, leistungId, leistungsPositionId, unitPrice, vatRatePercent, amount, margin, securitySurcharge, notes)
    }

    fun findProjectById(id: ObjectId): Project? {
        return projectConfiguratorApi.findProjectById(id)
    }

    fun findProjectByListingId(listingId: String): Project? {
        return projectConfiguratorApi.findProjectByListingId(listingId)
    }

    fun createProject(listingId: String) : Project {
        return projectConfiguratorApi.createProject(listingId)
    }

    fun getMassedaten(projectId: ObjectId): Map<String, Massedatum>? {
        return projectConfiguratorApi.getMassedaten(projectId)
    }

    fun archiveConfiguration(configurationId: ProjectConfigurationId) {
        return projectConfiguratorApi.archiveConfiguration(configurationId)
    }

    fun deleteLeistung(configurationId: ObjectId, leistungId: LeistungId) {
        projectConfiguratorApi.deleteLeistung(configurationId, leistungId)
    }

    fun deleteLeistungsPosition(configurationId: ObjectId, leistungId: LeistungId, leistungsPositionId: LeistungsPositionId) {
        projectConfiguratorApi.deleteLeistungsPosition(configurationId, leistungId, leistungsPositionId)
    }

    fun copyConfiguration(projectId: String, objectId: ObjectId, name: String): ProjectConfigurationId {
        return projectConfiguratorApi.copyConfiguration(projectId, objectId, name)
    }


}