<template>
  <v-app class="embedded">
    <v-main class="text-body-1 embedded pt-0">
      <router-view v-if="isAuthInitialized" :key="route.fullPath" />
      <d-loading-centered v-else />
      <v-dialog v-model="confirmationForm" max-width="400">
        <d-confirmation-form
          v-if="formData"
          :confirm="formData.confirm"
          :message="formData.message"
          :cancel-text="formData.cancelText"
          :confirm-text="formData.confirmText"
          @finish="formData = null"
        />
      </v-dialog>
    </v-main>
  </v-app>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router';
import { useAuth } from '@/service/auth/use-auth';
import DLoadingCentered from '@/components/ui/d-loading-centered.vue';
import DConfirmationForm from '@/components/ui/d-confirmation-form.vue';
import { initializeConfirmationService } from '@/service/use-confirmation-service';
import { provide, ref, watch } from 'vue';

const route = useRoute();
const { isAuthInitialized } = useAuth();
const { formData } = initializeConfirmationService(provide);
const confirmationForm = ref(false);
watch(formData, (val) => {
  confirmationForm.value = !!val;
});
</script>

<style lang="scss" scoped>
.embedded,
:deep(.v-application__wrap) {
  height: auto;
}
</style>

<style>
html,
body,
#app {
  height: auto;
}
.text-no-select {
  -webkit-user-select: none;
  user-select: none;
}
</style>
