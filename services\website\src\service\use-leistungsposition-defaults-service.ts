import { inject, InjectionKey, provide, ref } from 'vue';
import { DGetLeistungspositionDefaultsQuery } from '@/adapter/graphql/generated/graphql';

export type LeistungspositionDefaultsService = ReturnType<typeof initializeLeistungspositionDefaultsService>;

const leistungspositionDefaultsKey = Symbol(
  'leistungspositionDefaultsService'
) as InjectionKey<LeistungspositionDefaultsService>;

export interface LeistungspositionDefaults
  extends Omit<DGetLeistungspositionDefaultsQuery['leistungsPositionDefaults'], '__typename'> {}

export function initializeLeistungspositionDefaultsService(provideFn: typeof provide) {
  const defaults = ref<LeistungspositionDefaults | null>(null);

  const service = { defaults };

  provideFn<LeistungspositionDefaultsService>(leistungspositionDefaultsKey, service);
  return service;
}

export function useLeistungspositionDefaultsService(): LeistungspositionDefaultsService {
  return inject<LeistungspositionDefaultsService>(leistungspositionDefaultsKey)!;
}
