package com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming

import com.doorbit.projectconfigurator.projectconfigurator.domain.LeistungId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProjectConfigurationId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProductDatabaseId
import com.doorbit.projectconfigurator.projectconfigurator.domain.massedaten.Massedatum
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.ProductDatabase
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.SchnellkonfigurationType
import com.doorbit.projectconfigurator.projectconfigurator.domain.project.Project
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.LeistungsPositionId
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.ProjectConfiguration
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.SelectedLeistung
import org.bson.types.ObjectId
import java.math.BigDecimal

interface ProjectConfiguratorApi {

    fun loadProductDatabase(projectConfigurationId: ProjectConfigurationId) : ProductDatabase

    /**
     * Imports the product database, versions it and stores it.
     */
    fun importProductDatabase(versionName: String): ProductDatabaseId

    fun createConfiguration(projectId: String, productDatabaseId: ProductDatabaseId, name: String, schnellkonfigurationType: SchnellkonfigurationType?): ProjectConfigurationId

    fun getModuleNames(databaseVersion: String): List<String>
    fun getDatabaseVersions(): List<ProductDatabase>
    fun getProjectConfiguration(configurationId: ProjectConfigurationId): ProjectConfiguration

    fun findProjectById(id: ObjectId): Project?
    fun findProjectByListingId(listingId: String): Project?
    fun createProject(listingId: String): Project

    fun selectLeistung(configurationId: ProjectConfigurationId, leistungId: LeistungId, selection: Boolean)
    fun selectLeistungsPosition(configurationId: ProjectConfigurationId, leistungId: LeistungId, leistungsPositionId: LeistungsPositionId, selection: Boolean)
    fun changeExecution(configurationId: ProjectConfigurationId, leistungId: LeistungId, leistungExecutionType: SelectedLeistung.LeistungExecutionType)
    fun createLeistung(configurationId: ProjectConfigurationId, komponenteId: String, payload: LeistungCreateDto)
    fun createLeistungsPosition(configurationId: ProjectConfigurationId, leistungId: LeistungId, dto: LeistungsPositionCreateDto)
    fun configureLeistungsPosition(configurationId: ProjectConfigurationId, leistungId: LeistungId, leistungsPositionId: LeistungsPositionId, unitPrice: BigDecimal?, vatRatePercent: BigDecimal?, amount: BigDecimal?, margin: BigDecimal?, securitySurcharge: BigDecimal?, notes: String?)
    fun getMassedaten(projectId: ObjectId): Map<String, Massedatum>?
    fun archiveConfiguration(configurationId: ProjectConfigurationId)
    fun deleteLeistung(configurationId: ObjectId, leistungId: LeistungId)
    fun deleteLeistungsPosition(configurationId: ObjectId, leistungId: LeistungId, leistungsPositionId: LeistungsPositionId)
    fun copyConfiguration(projectId: String, configurationId: ProjectConfigurationId, name: String): ProjectConfigurationId

}