import { getVergabeEinheitenTableItem } from './getVergabeEinheitenTableItem';
import { VergabeEinheit } from '@/adapter/graphql/generated/graphql';

export function getVergabeEinheitenTableItems(
  vergabeEinheiten: VergabeEinheit[],
  locale: string,
  moduleToColorMap: { [moduleName: string]: string }
) {
  return vergabeEinheiten.flatMap((vergabeEinheit: VergabeEinheit) => {
    if (vergabeEinheit.komponenten.length === 0) {
      return getVergabeEinheitenTableItem({ locale, moduleToColorMap, vergabeEinheit });
    }
    return vergabeEinheit.komponenten.flatMap((komponente) => {
      if (komponente.leistung.length === 0) {
        return getVergabeEinheitenTableItem({
          locale,
          moduleToColorMap,
          vergabeEinheit,
          komponente
        });
      }
      return komponente.leistung.flatMap((leistung) => {
        if (leistung.leistungsPositionen.length === 0) {
          return getVergabeEinheitenTableItem({
            locale,
            moduleToColorMap,
            vergabeEinheit,
            komponente,
            leistung
          });
        }
        return leistung.leistungsPositionen.map((leistungsPosition) =>
          getVergabeEinheitenTableItem({
            locale,
            moduleToColorMap,
            vergabeEinheit,
            komponente,
            leistung,
            leistungsPosition
          })
        );
      });
    });
  });
}
