openapi: 3.0.0
info:
  title: Airtable API
  version: 1.0.0
  description: API-Dokumentation für den Zugriff auf Airtable-Daten
servers:
  - url: https://api.airtable.com/v0/{base_id}/{table_name}
    variables:
      base_id:
        default: your_base_id
        description: Die ID der Airtable-Basis
      table_name:
        default: your_table_name
        description: Der Name der Airtable-Tabelle
paths:
  /{base_id}/{table_id}:
    get:
      summary: Liste von Records abrufen
      description: Ruft eine Liste von Records aus der angegebenen Tabelle ab.
      operationId: getRecords
      tags:
        - AirtableProductDatabase
      parameters:
        - name: base_id
          in: path
          required: true
          schema:
            type: string
          description: Die ID der Airtable-Basis
        - name: table_id
          in: path
          required: true
          schema:
              type: string
          description: Der Name der Airtable-Tabelle
        - name: view
          in: query
          required: true
          schema:
            type: string
          description: Der Name der Ansicht, die verwendet werden soll
        - name: pageSize
          in: query
          required: false
          schema:
            type: integer
            default: 100
          description: The number of records returned in each request. Must be less than or equal to 100. Default is 100.
        - name: offset
          in: query
          required: false
          schema:
              type: string
          description: To fetch the next page of records, include offset from the previous request in the next request's parameters.
      responses:
        '200':
          description: Erfolgreiche Antwort
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AirtableResponse'
components:
  schemas:
    AirtableResponse:
      type: object
      required:
        - records
      properties:
        offset:
          type: string
          description: If there are more records, the response will contain an offset. Pass this offset into the next request to fetch the next page of records.
        records:
          type: array
          items:
            $ref: '#/components/schemas/Record'
    Record:
      type: object
      required:
        - id
        - createdTime
        - fields
      properties:
        id:
          type: string
        createdTime:
          type: string
          format: date-time
        fields:
          type: object
          additionalProperties: { }
