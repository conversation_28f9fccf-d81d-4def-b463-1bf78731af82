<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="BFF (local-to-integ)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <option name="ACTIVE_PROFILES" value="local-to-integ" />
    <envs>
      <env name="GRAPHQL_SCHEMA_LOCATION" value="api/graphql/schema" />
      <env name="AIRTABLE_TOKEN" value="**********************************************************************************" />
    </envs>
    <module name="backend-bff" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.doorbit.projectconfigurator.bff.Application" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.doorbit.projectconfigurator.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>