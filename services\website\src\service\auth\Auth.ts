import { Optional } from '@/model/Optional';
import { KeycloakProfile } from 'keycloak-js';
import { Ref } from 'vue';

export type Auth = {
  readonly isAuthInitialized: Readonly<Ref<boolean>>;
  readonly isLoggedIn: Readonly<Ref<boolean>>;
  readonly username: <PERSON>only<Ref<Optional<string>>>;
  readonly userId: Readonly<Ref<Optional<string>>>;
  readonly accessToken: Readonly<Ref<Optional<string>>>;
  readonly isAdminUser: Readonly<Ref<boolean>>;
  readonly ísPremiumUser: Readonly<Ref<boolean>>;

  triggerRegistration(redirectURL?: string): Promise<void>;
  triggerLogin(redirectURL?: string): Promise<void>;
  triggerPasswordChange(redirectURL?: string): Promise<void>;
  triggerLogout(redirectURL?: string): Promise<void>;
  openAccountManagement(): Promise<void>;
  fetchUserProfile(): Promise<KeycloakProfile>;
};
