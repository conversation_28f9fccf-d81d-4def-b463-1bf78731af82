<template>
  <v-row
    no-gutters
    class="d-fixed-app-bar position-fixed elevation-2 bg-shades-white"
    :style="{
      height: $vuetify.display.lgAndUp ? '48px' : '96px'
    }"
  >
    <v-col cols="12" lg="6" class="d-flex align-top">
      <v-btn
        v-if="isDev && displayContext === 'BACKOFFICE'"
        flat
        color="transparent"
        :icon="displayContext === 'BACKOFFICE' ? mdiHead : mdiHeadCheck"
        :rounded="false"
        @click="() => (displayContext = displayContext === 'BACKOFFICE' ? 'END_CUSTOMER' : 'BACKOFFICE')"
      />
      <v-btn
        v-if="displayContext === 'BACKOFFICE'"
        :rounded="false"
        flat
        color="transparent"
        :icon="mdiPlus"
        @click="creatingNewConfig = true"
      />
      <d-configurations />
    </v-col>
    <v-col v-if="configurationTable?.calculationResult" cols="12" lg="6" class="d-flex align-end justify-end">
      <div class="ma-3">
        <span class="text-subtitle-1 font-weight-regular">{{ t('domain.offerPriceNet') }}: </span>
        <span class="text-subtitle-1">{{
          currencyFormat(configurationTable.calculationResult.offerPriceNet, locale)
        }}</span>
      </div>
      <div class="ma-3">
        <span class="text-subtitle-1 font-weight-regular">{{ t('domain.vatAmount') }}: </span>
        <span class="text-subtitle-1">{{
          currencyFormat(configurationTable.calculationResult.vatAmount, locale)
        }}</span>
      </div>
      <div class="ma-3">
        <span class="text-subtitle-1 font-weight-regular">{{ t('domain.totalProjectCosts') }}: </span>
        <span class="text-subtitle-1">{{
          currencyFormat(configurationTable.calculationResult.offerPriceGross, locale)
        }}</span>
      </div>
    </v-col>
  </v-row>
  <div
    :style="{
      height: $vuetify.display.lgAndUp ? '48px' : '96px'
    }"
    class="bg-grey-lighten-4 w-100"
  />
  <v-dialog v-model="creatingNewConfig" max-width="500">
    <d-config-creation-form v-if="dbVersions.length" @finish="creatingNewConfig = false" />
    <d-generic-error v-else fullscreen :message="t('domain.configurations.noDbVersions')">
      <v-card-subtitle class="text-pre-wrap">{{ t('errorViews.consultAdmin') }}</v-card-subtitle>
    </d-generic-error>
  </v-dialog>
  <d-generic-error v-if="!configurations.length" fullscreen :message="t('domain.configurations.noneCreated')">
    <v-card-subtitle class="text-pre-wrap">
      {{ t('domain.configurations.createFirst') }}
    </v-card-subtitle>
  </d-generic-error>
  <d-configuration v-else />
  <d-vergabe-einheiten v-if="selectedConfiguration && configurationTable" />
</template>

<script setup lang="ts">
import DConfigurations from '@/components/content/d-configurations.vue';
import DVergabeEinheiten from '@/components/content/d-vergabe-einheiten.vue';
import { useProjectService } from '@/service/use-project-service';
import { useConfigurationTableService } from '@/service/use-configuration-table-service';
import DConfigCreationForm from '@/components/content/d-config-creation-form.vue';
import DConfiguration from '@/components/d-configuration.vue';
import DGenericError from '@/components/ui/d-generic-error.vue';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { mdiHead, mdiHeadCheck, mdiPlus } from '@mdi/js';
import { currencyFormat } from '@/utility/filter';

const { t, locale } = useI18n();
const { configurationTable } = useConfigurationTableService();
const creatingNewConfig = ref(false);
const { configurations, dbVersions, selectedConfiguration, isDev, displayContext } = useProjectService();
</script>

<style lang="scss" scoped>
.d-fixed-app-bar {
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
}
</style>
