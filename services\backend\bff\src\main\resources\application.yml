server:
  tomcat:
    threads:
      max: 750
  port: 8280

logging:
  level:
    root: INFO
    org.springframework: INFO
    org.springframework.security: INFO
    com.doorbit: TRACE

spring:
  cache:
    type: caffeine

  graphql:
    path: /graphql
    schema:
      locations: "file:${graphql_schema_location}"
    websocket:
      path: /subscriptions
      connection-init-timeout: 60s
    graphiql:
      path: /graphiql
      enabled: false
    tools:
      introspection-enabled: false
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 300MB # all images, we strip them down anyway to approx. 500KB each
      file-size-threshold: 999MB # We don't want temp files, so we set this to a very high value

management:
  health:
    diskSpace:
      enabled: false
  codec:
    max-in-memory-size: 10MB # wichtig für graphQL und große requests
  endpoints:
    enabled-by-default: false
    web.exposure.include: "*"
  endpoint:
    health:
      enabled: true
      show-details: always
#  metrics:
#    # Disables most OOTB micrometer metrics. All this stuff is already provided by GCP and GKE.
#    # Metrics ingestion into GCP Stackdriver is relatively expensive, so we want to keep the number of metrics low.
#    enable:
#      graphql: true
#      http: true
#      # These are disabled because they don't add value and lead to a lot of noise in prometheus (and costs)
#      graphql.datafetcher.active: false
#      graphql.request.active: false
#      http.server.requests.active: false
#      jvm: false
#      tomcat: false
#      system: false
#      executor: false
#      logback: false
#      disk: false
#      application: false
#      process: false
#      spring: false

#  prometheus:
#    metrics:
#      export:
#        enabled: false

#  logging:
#    metrics:
#      export:
#        enabled: false
#        # Extending the step from 1m to 5m would save us 80% of the metrics and thus 80% of the cost
#        step: 1m

application:
  cloudflare-cookie: ""
  # We ain't using the caffeine default implementation using caffeine.spec config property, because it cannot configure different TTLs for
  # different caches, etc. However we still configure the cache type to caffeine using spring.cache.type: caffeine.
  # Hence, we're creating caches on our own using the CacheConfig.kt class.
  # When requiring a new cache, just add it to the cacheNames list here and configure the cache accordingly.
  # You are then ready to go to use the cache by defining @Cacheable("your-cache-name") on the method you want to cache.
  cache:
    default:
      ttl-in-minutes: 1440
      max-size: 10000
  services:
    listing:
      host: http://localhost:8081/listing
    userprofile:
      host: http://localhost:8084/userprofile
  productDatabase:
    airtable:
      token: ${AIRTABLE_TOKEN}
  slack:
    enabled: false
    webhook: *********************************************************************************