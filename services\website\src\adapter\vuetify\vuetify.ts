import 'vuetify/styles';
import { createVuetify, type ThemeDefinition } from 'vuetify';
import { md3 } from 'vuetify/blueprints';
import { createVueI18nAdapter } from 'vuetify/locale/adapters/vue-i18n';
import vueI18n from '@/adapter/vue-i18n/vue-i18n';
import { useI18n } from 'vue-i18n';
import { aliases, mdi } from 'vuetify/iconsets/mdi-svg';

const configuratorTheme: ThemeDefinition = {
  colors: {
    primary: '#F78552',
    primaryLight: '#FBC2A8',
    secondary: '#E5F0DE',
    secondaryLight: '#F5F9F2',
    success: '#46866B'
  }
};

// https://vuetifyjs.com/en/introduction/why-vuetify/#feature-guides
export default createVuetify({
  components: {
    //BETA STUFF: https://vuetifyjs.com/en/labs/introduction/
  },
  blueprint: md3, //Material Design 3
  defaults: {
    //...
  },
  icons: {
    defaultSet: 'mdi',
    aliases: {
      ...aliases
    },
    sets: {
      mdi
    }
  },
  locale: {
    adapter: createVueI18nAdapter({
      i18n: vueI18n,
      useI18n
    })
  },
  theme: {
    defaultTheme: 'configuratorTheme', // 'light' is default
    themes: { configuratorTheme }
  }
});
