# GraphQL-Code-Generator für Kotlin

Das Ausführen von `generate` generiert alle GraphQL-DTOs und schreibt sie in die Datei `GraphQLDTOs.kt` im Package `com.doorbit.bff.infra.api.dto`.
Im Anschluss wird die Datei nochmal durch die `kotlin-patch.js`-Datei bearbeitet.

<PERSON><PERSON>lder, die im GraphQL-Schema mit `#schema-mapping` markiert wurden, müssen aus den erzeugten DTOs entfernt werden.
Dafür wird die `kotlin-patch.js`-Datei verwendet.

All<PERSON> Felder, die im GraphQL-Schema mit `#schema-mapping` markiert wurden, müssen in den Controllern mithilfe von
`@SchemaMapping(…)`-Annotationen aufgelöst werden.