FROM eclipse-temurin:21-jdk
LABEL org.opencontainers.image.authors="Doorbit Team"

USER root

RUN apt-get update && apt-get install -y 'tzdata' 'ca-certificates' && update-ca-certificates && \
    cp /usr/share/zoneinfo/Europe/Berlin /etc/localtime && \
    echo "Europe/Berlin" > /etc/timezone && \
    groupadd --system dbt && useradd --system -g dbt dbt

USER dbt

ARG target
ARG graphqlSchema

COPY $target/app.jar /app.jar
COPY $graphqlSchema /schema

ENTRYPOINT ["java", "-XX:MaxRAMPercentage=80", "-jar", "app.jar"]