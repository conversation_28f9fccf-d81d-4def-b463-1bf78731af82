<template>
  <v-card v-if="leistungDetailsData" :rounded="!isFullscreen">
    <div class="pb-4">
      <v-card-title class="d-flex align-center text-pre-wrap d-leistung__title">
        <v-icon size="40" class="mr-4">
          <d-icon-leistung-addon v-if="leistungDetailsData.leistungDetails.type === 'ADD_ON'" />
          <d-icon-leistung-custom v-else-if="leistungDetailsData.leistungDetails.type === 'ALTERNATIVE'" />
          <d-icon-leistung-variant v-else-if="leistungDetailsData.leistungDetails.type === 'ZERO_VARIANT'" />
          <d-icon-leistung-standard v-else />
        </v-icon>
        {{ leistungDetailsData.leistungDetails.displayName }}
        <v-spacer />
        <v-btn flat class="align-self-start" :icon="mdiClose" color="white" @click="unselect" />
      </v-card-title>
      <v-card-subtitle class="mx-2 text-pre-wrap">
        {{
          [
            leistungenWithParentNames[activeLeistungIndex].moduleName,
            leistungenWithParentNames[activeLeistungIndex].vergabeEinheitName,
            leistungenWithParentNames[activeLeistungIndex].komponenteName
          ].join(' > ')
        }}
      </v-card-subtitle>
    </div>
    <d-leistung-infos :leistung-details-data="leistungDetailsData" @new-table-data="updateTableAndReloadDetails" />
    <d-leistungs-positionen
      :leistung-details-data="leistungDetailsData"
      @new-table-data="updateTableAndReloadDetails"
    />
    <v-spacer />
    <v-card-actions class="justify-end">
      <v-btn color="black" :disabled="isPrevDisabled" @click="toPrev">
        <template #prepend>
          <v-icon :icon="mdiArrowULeftTop" />
        </template>
        {{ t('common.prev') }}
      </v-btn>
      <v-btn color="black" :disabled="isNextDisabled" @click="toNext">
        <template #prepend>
          <v-icon :icon="mdiArrowURightBottom" />
        </template>
        {{ t('common.next') }}
      </v-btn>
      <v-btn
        :disabled="displayContext === 'END_CUSTOMER'"
        variant="tonal"
        :prepend-icon="leistungDetailsData.leistungDetails.isSelected ? mdiCheckboxMarkedCircle : undefined"
        :color="leistungDetailsData.leistungDetails.isSelected ? 'success' : 'primary'"
        @click="toggleSelect"
        >{{
          leistungDetailsData.leistungDetails.isSelected
            ? t('domain.leistung.selected')
            : t('domain.leistung.notSelected')
        }}</v-btn
      >
    </v-card-actions>
  </v-card>
</template>

<script lang="ts" setup>
import {
  ConfigurationTable,
  DGetLeistungDetailsQuery,
  useDGetLeistungDetailsLazyQuery
} from '@/adapter/graphql/generated/graphql';
import DIconLeistungAddon from '@/components/content/leistung-type/d-icon-leistung-addon.vue';
import DIconLeistungCustom from '@/components/content/leistung-type/d-icon-leistung-custom.vue';
import DIconLeistungStandard from '@/components/content/leistung-type/d-icon-leistung-standard.vue';
import DIconLeistungVariant from '@/components/content/leistung-type/d-icon-leistung-variant.vue';
import { computed, ref, watch } from 'vue';
import { mdiArrowULeftTop, mdiArrowURightBottom, mdiClose, mdiCheckboxMarkedCircle } from '@mdi/js';
import { useI18n } from 'vue-i18n';
import { useProjectService } from '@/service/use-project-service';
import { useConfigurationTableService } from '@/service/use-configuration-table-service';
import DLeistungInfos from '@/components/content/leistung/d-leistung-infos.vue';
import DLeistungsPositionen from '@/components/content/leistung/d-leistungs-positionen.vue';

const { t } = useI18n();
const { selectedConfiguration, displayContext } = useProjectService();
const { changeLeistungSelection, setConfigTable, leistungenWithParentNames } = useConfigurationTableService();
defineProps<{ isFullscreen: boolean }>();

const {
  variables: leistungDetailsQueryVars,
  load: loadLeistungsDetails,
  onResult: onDetailsLoaded,
  refetch
} = useDGetLeistungDetailsLazyQuery();

onDetailsLoaded((res) => {
  leistungDetailsData.value = res.data;
});

const leistungDetailsData = ref<DGetLeistungDetailsQuery | null>(null);
const leistungId = defineModel<string | null>({ required: true });
watch(
  leistungId,
  async (value) => {
    if (value) {
      leistungDetailsQueryVars.value = {
        configurationId: selectedConfiguration.value!.configurationId,
        leistungId: value,
        displayContext: displayContext.value
      };
      loadLeistungsDetails();
    }
  },
  { immediate: true }
);

async function updateTableAndReloadDetails(tableData?: ConfigurationTable) {
  if (!tableData) return;
  setConfigTable(tableData);
  const res = (await refetch())?.data;
  if (res) {
    leistungDetailsData.value = res;
  }
}

const activeLeistungIndex = computed(() =>
  leistungenWithParentNames.value.findIndex((leistungIter) => leistungId.value === leistungIter.leistungId)
);

const isPrevDisabled = computed(() => activeLeistungIndex.value <= 0);
const isNextDisabled = computed(() => activeLeistungIndex.value + 1 >= leistungenWithParentNames.value.length);

function unselect() {
  leistungId.value = null;
}

function toPrev() {
  leistungId.value = leistungenWithParentNames.value[activeLeistungIndex.value - 1].leistungId;
}

function toNext() {
  leistungId.value = leistungenWithParentNames.value[activeLeistungIndex.value + 1].leistungId;
}

async function toggleSelect() {
  const result = await changeLeistungSelection({
    leistungId: leistungId.value!,
    selection: !leistungDetailsData.value!.leistungDetails.isSelected,
    configurationId: selectedConfiguration.value!.configurationId
  });
  await updateTableAndReloadDetails(result?.data?.selectLeistung as ConfigurationTable | undefined);
}
</script>

<style scoped>
.d-leistung__title {
  white-space: normal !important;
}
</style>
