package com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration

import com.doorbit.projectconfigurator.projectconfigurator.domain.LeistungId
import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.toMoney
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.DbLeistung
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException
import java.math.BigDecimal

class Leistungen(
    private val selectedLeistungen: MutableList<SelectedLeistung> = mutableListOf(),
) {

    fun selectLeistung(leistungId: String, selection: Boolean): SelectedLeistung {
        val existing = selectedLeistung(leistungId, false)
        if (existing != null) {
            existing.changeSelectionState(selection)
            return existing
        }

        val leistung = SelectedLeistung(leistungId, selection)
        selectedLeistungen.add(leistung)

        return leistung
    }

    fun deleteLeistung(leistungId: LeistungId) : SelectedLeistung? {
        val leistung = selectedLeistungen.find { it.id == leistungId }
        selectedLeistungen.removeIf { it.id == leistungId }
        return leistung
    }

    fun deleteLeistungsPosition(leistungId: LeistungId, leistungsPositionId: LeistungsPositionId) {
        val leistung = leistungByIdOrThrow(leistungId)
        leistung.configuredLeistungsPositionen.removeIf { it.id == leistungsPositionId }
    }

    fun isLeistungSelected(recordId: String): Boolean = selectedLeistungen.find { it.id == recordId }?.isSelected ?: false

    fun changeExecution(leistungId: LeistungId, leistungExecutionType: SelectedLeistung.LeistungExecutionType) {
        if (!isLeistungSelected(leistungId)) {
            selectLeistung(leistungId, true)
        }

        leistungByIdOrThrow(leistungId).execution = leistungExecutionType
    }

    private fun leistungByIdOrThrow(leistungId: LeistungId) =
        selectedLeistungen.find { it.id == leistungId } ?: throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Leistung with Id $leistungId not found")

    fun selectedLeistung(leistungId: LeistungId, returnSelectedOnly: Boolean = true): SelectedLeistung? {
        val leistung = selectedLeistungen.find { it.id == leistungId }
        if (!returnSelectedOnly) {
            // Selection state is not important
            return leistung
        }

        return leistung?.takeIf(SelectedLeistung::isSelected)
    }

    fun selectLeistungsPosition(leistungId: LeistungId, leistungsPositionId: LeistungsPositionId, selection: Boolean, dbLeistung: DbLeistung) {

        if (!isLeistungSelected(leistungId)) {
            selectLeistung(leistungId, true)
        }

        val leistung = leistungByIdOrThrow(leistungId)

        if (!isLeistungsPositionConfigured(leistung, leistungsPositionId)) {
            configureLeistungsPosition(leistungId, leistungsPositionId, null, null, null, null, null, null, dbLeistung)
        }

        leistung.configuredLeistungsPosition(leistungsPositionId)!!.isSelected = selection
    }

    private fun isLeistungsPositionConfigured(leistung: SelectedLeistung, leistungsPositionId: LeistungsPositionId) = leistung.configuredLeistungsPosition(leistungsPositionId) != null

    fun configureLeistungsPosition(leistungId: LeistungId, leistungsPositionId: LeistungsPositionId, unitPrice: BigDecimal?, vatRatePercent: BigDecimal?, amount: BigDecimal?, margin: BigDecimal?, securitySurcharge: BigDecimal?, notes: String?, dbLeistung: DbLeistung): ConfiguredLeistungsPosition {

        val lp = dbLeistung.leistungsPosition(leistungsPositionId)

        if (!isLeistungSelected(leistungId)) {
            selectLeistung(leistungId, true)
        }

        return selectedLeistung(leistungId)!!
            .configureLeistungsPosition(leistungsPositionId, unitPrice, vatRatePercent, amount, margin, securitySurcharge, notes, lp)
    }

    fun calculate() {
        selectedLeistungen.forEach(SelectedLeistung::calculate)
    }

    fun totalNetCosts(): BigDecimal {
        return selectedLeistungen
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.netCosts } }.toMoney()
    }

    fun totalOfferPriceNet(): BigDecimal {
        return selectedLeistungen
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.offerPriceNet } }.toMoney()
    }

    fun totalVatAmount(): BigDecimal {
        return selectedLeistungen
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.vatAmount } }.toMoney()
    }

    fun totalOfferPriceGross(): BigDecimal {
        return selectedLeistungen
            .sumOf { leistung -> leistung.totalOfferPriceGross() }.toMoney()
    }

    fun totalMargin(): BigDecimal {
        return selectedLeistungen
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.margin } }.toMoney()
    }

    fun totalSecuritySurcharge(): BigDecimal {
        return selectedLeistungen
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.securitySurcharge } }.toMoney()
    }

    fun totalNetCostsByLeistungIds(alleLeistungen: List<String>): BigDecimal {
        return selectedLeistungen
            .filter { it.id in alleLeistungen }
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.netCosts } }.toMoney()
    }

    fun totalOfferPriceGrossByLeistungIds(alleLeistungen: List<String>): BigDecimal {
        return selectedLeistungen
            .filter { it.id in alleLeistungen }
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.offerPriceGross } }.toMoney()
    }

    fun totalOfferPriceNetByLeistungIds(allLeistungenIds: List<String>): BigDecimal {
        return selectedLeistungen
            .filter { it.id in allLeistungenIds }
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.offerPriceNet } }.toMoney()
    }

    fun totalMarginByLeistungIds(allLeistungenIds: List<String>): BigDecimal {
        return selectedLeistungen
            .filter { it.id in allLeistungenIds }
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.margin } }.toMoney()
    }

    fun totalVatAmountByLeistungIds(allLeistungenIds: List<String>): BigDecimal {
        return selectedLeistungen
            .filter { it.id in allLeistungenIds }
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.vatAmount } }.toMoney()
    }

    fun totalSecuritySurchargeByLeistungIds(allLeistungenIds: List<String>): BigDecimal {
        return selectedLeistungen
            .filter { it.id in allLeistungenIds }
            .sumOf { leistung -> leistung.configuredLeistungsPositionen.sumOf { lp -> lp.calculation.securitySurcharge } }.toMoney()
    }


}