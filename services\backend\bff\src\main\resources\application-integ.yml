logging:
  level:
    root: INFO
    org.springframework: WARN
    org.springframework.security: INFO
    com.doorbit: DEBUG

spring:
  data:
    mongodb:
      uri: mongodb+srv://${MONGO_USER}:${MONGO_PW}@projectconfigurator.e9yloby.mongodb.net/?retryWrites=true&w=majority
      database: projectconfigurator
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://auth-integ.doorbit.com/login/realms/doorbit
          jwk-set-uri: https://auth-integ.doorbit.com/login/realms/doorbit/protocol/openid-connect/certs
  graphql:
    schema:
      locations: "file:/schema"

management:
#  prometheus:
#    metrics:
#      export:
#        enabled: true
#
#  logging:
#    metrics:
#      export:
#        enabled: false

server:
  port: 8080

application:
  services:
    listing:
      host: http://listing-microservice-service.listing-microservice/listing
    userprofile:
      host: http://userprofile-microservice-service.userprofile-microservice/userprofile
  slack:
    enabled: true