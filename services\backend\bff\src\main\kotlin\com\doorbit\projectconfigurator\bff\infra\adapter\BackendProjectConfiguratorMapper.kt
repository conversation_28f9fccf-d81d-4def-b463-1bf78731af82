package com.doorbit.projectconfigurator.bff.infra.adapter

import com.doorbit.projectconfigurator.bff.infra.api.dto.LeistungInput
import com.doorbit.projectconfigurator.bff.infra.api.dto.LeistungsPositionInput
import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.LeistungCreateDto
import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.LeistungsPositionCreateDto
import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.toMoney
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.LeistungType
import java.math.BigDecimal

object BackendProjectConfiguratorMapper {
    fun toCustomLeistungCreateDto(payload: LeistungInput): LeistungCreateDto {
        return LeistungCreateDto(
            displayName = payload.displayName,
            leistungType = LeistungType.valueOf(payload.leistungType.name),
            offerPrice = payload.offerPrice,
            saveAsGlobal = payload.globallyAvailable
        )
    }

    fun toCustomLeistungsPositionCreateDto(payload: LeistungsPositionInput): LeistungsPositionCreateDto {
        return LeistungsPositionCreateDto(
            displayName = payload.displayName,
            unitPrice = BigDecimal(payload.unitPrice).toMoney(),
            vatRatePercent = BigDecimal(payload.vatRatePercent).toMoney(),
            marginPercent = BigDecimal(payload.marginPercent).toMoney(),
            securitySurchargePercent = BigDecimal(payload.securitySurchargePercent).toMoney(),
            amount = payload.amount.let { BigDecimal(it).toMoney() },
            saveAsGlobal = payload.globallyAvailable,
            unit = payload.unit,
            notes = payload.notes
        )
    }
}