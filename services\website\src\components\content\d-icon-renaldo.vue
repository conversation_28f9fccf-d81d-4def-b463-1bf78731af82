<template>
  <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="139.71 101.16 122.14 199.86">
    <path
      :fill="current.colors.primary"
      stroke-width="0"
      d="M206.06,301.02c-.4,0-.78-.18-1.03-.49-.25-.31-.36-.7-.29-1.09,1.67-9.05,7.89-18.55,18.51-28.22l6.37-5.81h-42.94c-25.83,0-46.89-20.81-46.97-46.38-.03-12.43,4.79-24.13,13.59-32.96,8.79-8.83,20.48-13.69,32.9-13.69h14.56c13.59,0,24.81-10.91,25.01-24.32.1-6.66-2.42-12.95-7.1-17.71-4.68-4.76-10.93-7.38-17.59-7.38h-36.23c-1.84,0-3.34,1.5-3.34,3.34v18.39c0,12.76-9.29,23.29-21.79,24.93v-67.13c0-.74.6-1.34,1.34-1.34h59.54c25.83,0,46.89,20.81,46.97,46.38.03,12.43-4.79,24.13-13.58,32.96-8.8,8.83-20.48,13.69-32.9,13.69h-14.56c-13.59,0-24.81,10.91-25.01,24.32-.1,6.67,2.42,12.95,7.1,17.71,4.68,4.76,10.93,7.38,17.58,7.38h43.42l-6.37-5.81c-10.62-9.68-16.84-19.18-18.51-28.22-.07-.39.03-.79.29-1.09.26-.31.63-.49,1.03-.49h20.38c.09,0,.***********,1.69,18.73,16.43,33.57,35.06,35.27.1,0,.18.08.18.17v21.79c0,.09-.07.16-.18.17-18.63,1.7-33.38,16.54-35.06,35.27,0,.1-.08.18-.17.18h-20.38Z"
    ></path>
  </svg>
</template>
<script setup lang="ts">
import { useTheme } from 'vuetify';
const { current } = useTheme();
</script>
