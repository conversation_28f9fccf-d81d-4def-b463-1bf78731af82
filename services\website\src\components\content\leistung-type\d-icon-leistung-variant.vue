<template>
  <svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M46.4549 31.9271L35.8271 21.2993C33.4839 18.9561 29.6849 18.9561 27.3418 21.2993L16.714 31.9271C14.3708 34.2702 14.3708 38.0692 16.714 40.4124L27.3418 51.0402C29.6849 53.3833 33.4839 53.3833 35.8271 51.0402L46.4549 40.4124C48.798 38.0692 48.798 34.2702 46.4549 31.9271Z"
      :stroke="current.colors.primaryLight as string"
      stroke-width="3"
      stroke-miterlimit="10"
    />
    <path
      d="M55.3073 31.6129L44.6795 20.9851C42.3363 18.6419 38.5373 18.6419 36.1942 20.9851L25.5664 31.6129C23.2232 33.956 23.2232 37.755 25.5664 40.0982L36.1942 50.726C38.5373 53.0691 42.3363 53.0691 44.6795 50.726L55.3073 40.0982C57.6504 37.755 57.6504 33.956 55.3073 31.6129Z"
      :fill="current.colors.primary"
      :stroke="current.colors.primary"
      stroke-width="3"
      stroke-miterlimit="10"
    />
    <path
      d="M36 1.31982C34.29 1.31982 32.61 1.43982 30.96 1.67982C29.76 1.85982 28.59 2.09982 27.42 2.39982L29.91 4.88982L37.95 12.9298L40.08 10.7998L33.66 4.40982C34.44 4.34982 35.22 4.31982 36 4.31982C51.33 4.31982 64.14 15.3598 66.93 29.8798L69.45 27.3598C65.67 12.4198 52.11 1.31982 36 1.31982Z"
      :fill="current.colors.primary"
    />
    <path
      d="M48.21 64.8301L42.21 58.8301L40.08 60.9601L45.09 65.9701C42.21 66.8401 39.15 67.3201 36 67.3201C22.98 67.3201 11.79 59.3701 7.01996 48.0901L4.73996 50.3701C10.2 62.1601 22.17 70.3201 36 70.3201C40.02 70.3201 43.86 69.6301 47.46 68.3401C48.51 68.0101 49.5 67.5901 50.49 67.1101L48.21 64.8301Z"
      :fill="current.colors.primary"
    />
  </svg>
</template>
<script setup lang="ts">
import { useTheme } from 'vuetify';
const { current } = useTheme();
</script>
