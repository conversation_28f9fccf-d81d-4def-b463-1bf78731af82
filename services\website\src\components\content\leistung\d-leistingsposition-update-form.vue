<template>
  <v-dialog max-width="700">
    <template #activator="{ props: activatorProps }">
      <v-btn color="transparent" flat :icon="mdiPencil" v-bind="activatorProps" />
    </template>
    <template #default="{ isActive }">
      <v-card>
        <v-card-title class="text-pre-wrap">{{ displayName }}</v-card-title>
        <v-card-subtitle v-if="calculationFormula" class="text-pre-wrap"
          >{{ t('domain.calculationFormula') }}: {{ calculationFormula }}</v-card-subtitle
        >
        <v-card-subtitle v-if="description" class="my-2 text-pre-wrap"
          >{{ t('domain.leistungsposition.description') }}: {{ description }}</v-card-subtitle
        >
        <v-form v-model="valid" @submit.prevent="onSubmit">
          <v-row>
            <v-col cols="6">
              <div class="mt-2 mx-4 text-subtitle-2 font-weight-regular">
                {{ t('domain.leistungsposition.creationInstruction') }}
              </div>
              <v-card-text>
                <v-text-field
                  v-model.number="amount"
                  type="number"
                  min="0"
                  :label="t('domain.amount')"
                  rounded
                  :prefix="unit ?? ''"
                  variant="outlined"
                  density="compact"
                  autofocus
                  tabindex="0"
                />
                <v-text-field
                  v-model.number="unitPrice"
                  type="number"
                  min="0"
                  :label="t('domain.unitPrice')"
                  prefix="€"
                  rounded
                  variant="outlined"
                  density="compact"
                  tabindex="0"
                />
                <v-text-field
                  v-model.number="margin"
                  type="number"
                  min="0"
                  max="100"
                  :label="t('domain.margin')"
                  prefix="%"
                  rounded
                  variant="outlined"
                  density="compact"
                  tabindex="0"
                />
                <v-text-field
                  v-model.number="securitySurcharge"
                  type="number"
                  min="0"
                  max="100"
                  :label="t('domain.securitySurcharge')"
                  prefix="%"
                  rounded
                  variant="outlined"
                  density="compact"
                  tabindex="0"
                />
                <v-text-field
                  v-model.number="vatRatePercent"
                  type="number"
                  min="0"
                  max="100"
                  :label="t('domain.vatRate')"
                  prefix="%"
                  rounded
                  variant="outlined"
                  density="compact"
                  tabindex="0"
                />
                <v-textarea
                  v-model.trim="notes"
                  :on-click:clear="() => (notes = '')"
                  rounded
                  rows="2"
                  density="compact"
                  variant="outlined"
                  :label="t('domain.notes')"
                  tabindex="0"
                />
              </v-card-text>
            </v-col>
            <v-col cols="6" class="pr-4">
              <d-massedaten @selected="amount = $event ?? amount" />
            </v-col>
          </v-row>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="grey" :text="t('common.cancel')" tabindex="-1" @click="isActive.value = false" />
            <v-btn
              class="d-none"
              tabindex="-1"
              color="red"
              :text="t('common.delete')"
              @click="
                formData = {
                  message: 'Wollen Sie die Leistungsposition wirklich löschen?',
                  confirm: () => {}
                }
              "
            />
            <v-btn
              variant="tonal"
              tabindex="0"
              type="submit"
              :disabled="!valid"
              :text="t('common.save')"
              @click="isActive.value = false"
            />
          </v-card-actions>
        </v-form>
      </v-card>
    </template>
  </v-dialog>
</template>

<script lang="ts" setup>
import { mdiPencil } from '@mdi/js';
import { ref, watch } from 'vue';
import { LeistungspositionUpdateForm } from '@/components/content/leistung/LeistungspositionUpdateForm';
import { roundCurrency, roundPercentage } from '@/utility/rounding';
import { useConfirmationService } from '@/service/use-confirmation-service';
import { useI18n } from 'vue-i18n';
import DMassedaten from '@/components/content/d-massedaten.vue';

const { formData } = useConfirmationService();
const { t } = useI18n();

const props = defineProps<{
  defaults: LeistungspositionUpdateForm;
  displayName: string;
  description?: string | null;
  calculationFormula?: string | null;
  unit?: string | null;
}>();
const emits = defineEmits<{ submit: [form: LeistungspositionUpdateForm] }>();

const valid = ref(false);
const unitPrice = ref(props.defaults.unitPrice);
const vatRatePercent = ref(props.defaults.vatRatePercent);
const amount = ref(props.defaults.amount);
const notes = ref(props.defaults.notes ?? '');
const margin = ref(props.defaults.margin);
const securitySurcharge = ref(props.defaults.securitySurcharge);

watch(
  props,
  (newProps) => {
    valid.value = false;
    unitPrice.value = newProps.defaults.unitPrice;
    vatRatePercent.value = newProps.defaults.vatRatePercent;
    amount.value = newProps.defaults.amount;
    margin.value = newProps.defaults.margin;
    securitySurcharge.value = newProps.defaults.securitySurcharge;
  },
  { immediate: true }
);

function onSubmit() {
  emits('submit', {
    unitPrice: roundCurrency(unitPrice.value),
    vatRatePercent: roundPercentage(vatRatePercent.value / 100),
    amount: amount.value,
    margin: roundPercentage(margin.value / 100),
    securitySurcharge: roundPercentage(securitySurcharge.value / 100),
    notes: notes.value
  });
  // TODO: move backend handling here and submit only on success otherwise retry logic within dialog
  valid.value = false;
}
</script>

<style scoped></style>
