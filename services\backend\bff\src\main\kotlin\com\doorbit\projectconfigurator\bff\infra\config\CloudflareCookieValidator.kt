package com.doorbit.projectconfigurator.bff.infra.config

import com.nimbusds.jwt.JWTParser
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.Date
import java.util.regex.Pattern
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.context.event.EventListener
import kotlin.system.exitProcess

@Configuration
@Profile("local-to-integ")
class CloudflareCookieValidator(
    @Value(value = "\${application.cloudflare-cookie}") private val cfCookieValue: String,
) {

    @EventListener(ApplicationReadyEvent::class)
    fun validate() {
        val cookie = cfCookieValue
        val matcher = JWT_PATTERN.matcher(cookie)
        if (matcher.find()) {
            val jwt = matcher.group(1)
            val parsedJWT = JWTParser.parse(jwt)

            val claim = parsedJWT.jwtClaimsSet.getClaim("exp") as Date
            if (Instant.now().plus(4, ChronoUnit.DAYS).isAfter(claim.toInstant())) {
                println("The jwt token for cloudflare has expired. Please create a new one before you proceed.")
                exitProcess(1)
            }
        }
    }

    fun cookie(): String? = cfCookieValue

    companion object {
        val JWT_PATTERN: Pattern = Pattern.compile("CF_Authorization=(.*?);")
    }

}