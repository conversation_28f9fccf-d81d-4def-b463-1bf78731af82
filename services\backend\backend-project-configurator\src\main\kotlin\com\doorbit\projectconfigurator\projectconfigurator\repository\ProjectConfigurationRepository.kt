package com.doorbit.projectconfigurator.projectconfigurator.repository

import com.doorbit.projectconfigurator.projectconfigurator.domain.ProjectConfigurationId
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.ProjectConfiguration
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.core.env.Environment
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.stereotype.Repository

@Repository
class ProjectConfigurationRepository(
    private val mongoTemplate: MongoTemplate,
    environment: Environment
) : RepositoryBase(ProjectConfiguration::class.java.simpleName, environment) {

    @CacheEvict("projectConfiguration", key = "#projectConfiguration.id")
    fun save(projectConfiguration: ProjectConfiguration) {
        mongoTemplate.save(projectConfiguration)
    }

    @Cacheable("projectConfiguration", key = "#id")
    fun findById(id: ProjectConfigurationId): ProjectConfiguration {
        return mongoTemplate.findById(id, ProjectConfiguration::class.java)!!
    }
}