package com.doorbit.projectconfigurator.bff.infra.api

import com.doorbit.projectconfigurator.bff.core.domain.extension.WithLogger
import com.doorbit.projectconfigurator.bff.core.domain.extension.d
import com.doorbit.projectconfigurator.bff.infra.adapter.ProjectConfiguratorApiAdapter
import com.doorbit.projectconfigurator.bff.infra.api.dto.DisplayContext
import com.doorbit.projectconfigurator.bff.infra.api.dto.Project
import org.bson.types.ObjectId
import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.QueryMapping
import org.springframework.http.HttpStatus.NOT_FOUND
import org.springframework.stereotype.Controller
import org.springframework.web.server.ResponseStatusException

@Controller
class ProjectQueries(
    private val projectConfiguratorApiAdapter: ProjectConfiguratorApiAdapter,
    private val configurationTableService: ConfigurationTableService
) {

    @QueryMapping
    fun project(@Argument id: String, @Argument displayContext: DisplayContext): Project {

        LOGGER.d { "project($id) requested" }

        val project = projectConfiguratorApiAdapter.findProjectById(ObjectId(id)) ?: mockProject(id)

        return Project(
            id = project.id.toHexString(),
            listingId = project.listingId,
            configurations = project.configurationIds
                .mapNotNull { configurationTableService.findConfigurationTable(it.toHexString(), displayContext) }.toList()
        )
    }

    private fun mockProject(id: String): com.doorbit.projectconfigurator.projectconfigurator.domain.project.Project {
        LOGGER.d { "mockProject($id) requested" }

        return com.doorbit.projectconfigurator.projectconfigurator.domain.project.Project(
            id = ObjectId(),
            listingId = "mockedListingId",
            configurationIds = emptyList()
        )
    }


    @QueryMapping
    fun projectByListingId(@Argument listingId: String, @Argument displayContext: DisplayContext): Project {

        LOGGER.d { "projectByListingId($listingId) requested" }

        val project = projectConfiguratorApiAdapter.findProjectByListingId(listingId) ?: throw ResponseStatusException(NOT_FOUND, "Project with listingId $listingId not found")

        return Project(
            id = project.id.toHexString(),
            listingId = project.listingId,
            configurations = project.configurationIds.mapNotNull { configurationTableService.findConfigurationTable(it.toHexString(), displayContext) }.toList()
        )
    }

    companion object : WithLogger()

}