package com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration

import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.toMoney
import java.math.BigDecimal

data class ConfiguredLeistungsPosition(
    val id: LeistungsPositionId,
    val unitPrice: BigDecimal,
    val vatRatePercent: BigDecimal,
    val marginPercent: BigDecimal,
    val securitySurchargePercent: BigDecimal,
    val quantity: BigDecimal,
    val notes : String?,
    var isSelected: Boolean = true,
    var calculation: CalculatedLeistungsPosition = CalculatedLeistungsPosition.EMPTY
) {

    fun change(unitPrice: BigDecimal?, vatRatePercent: BigDecimal?, amount: BigDecimal?, margin: BigDecimal?, securitySurcharge: BigDecimal?, notes: String?): ConfiguredLeistungsPosition {
        val unitPriceToUse = unitPrice?.toMoney() ?: this.unitPrice
        val vatRateToUse = vatRatePercent?.toMoney() ?: this.vatRatePercent
        val amountToUse = amount?.toMoney() ?: this.quantity
        val marginToUse = margin?.toMoney() ?: this.marginPercent
        val securitySurchargeToUse = (securitySurcharge ?: BigDecimal.ZERO).toMoney()
        return copy(unitPrice = unitPriceToUse, vatRatePercent = vatRateToUse, marginPercent = marginToUse, quantity = amountToUse, securitySurchargePercent = securitySurchargeToUse, notes = notes)
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ConfiguredLeistungsPosition

        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    fun calculate() {
        if (!isSelected) {
            setCalculationToZero()
            return
        }

        val netCosts = unitPrice * quantity
        val securitySurcharge = netCosts * securitySurchargePercent
        val margin = (netCosts + securitySurcharge) * marginPercent

        // Totals
        val offerPriceNet = netCosts + margin + securitySurcharge
        val vat = offerPriceNet * vatRatePercent
        val offerPriceGross = offerPriceNet + vat

        this.calculation = CalculatedLeistungsPosition(
            offerPriceGross = offerPriceGross.toMoney(),
            offerPriceNet = offerPriceNet.toMoney(),
            vatAmount = vat.toMoney(),
            netCosts = netCosts.toMoney(),
            margin = margin.toMoney(),
            securitySurcharge = securitySurcharge.toMoney()
        )
    }

    fun setCalculationToZero() {
        this.calculation = CalculatedLeistungsPosition.EMPTY
    }
}