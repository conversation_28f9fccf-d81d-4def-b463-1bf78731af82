package com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration

import java.math.BigDecimal
import kotlin.test.Test
import kotlin.test.assertEquals

class ConfiguredLeistungsPositionTest {

    @Test
    fun `calculation test`() {
        // GIVEN a configured Leistungsposition
        val lp = ConfiguredLeistungsPosition(
            id = "Some LP",
            unitPrice = BigDecimal(1021),
            vatRatePercent = BigDecimal(0.19),
            marginPercent = BigDecimal(0.2),
            securitySurchargePercent = BigDecimal(0.05),
            quantity = BigDecimal(10),
            notes = null
        )

        // When calculate is called
        lp.calculate()

        // THEN the calculation is correct
        assertEquals(10210.00, lp.calculation.netCosts.toDouble())
        assertEquals(2144.10, lp.calculation.margin.toDouble())
        assertEquals(510.50, lp.calculation.securitySurcharge.toDouble())
        assertEquals(2444.27, lp.calculation.vatAmount.toDouble())
        assertEquals(12864.60, lp.calculation.offerPriceNet.toDouble())
        assertEquals(15308.87, lp.calculation.offerPriceGross.toDouble())
    }

}