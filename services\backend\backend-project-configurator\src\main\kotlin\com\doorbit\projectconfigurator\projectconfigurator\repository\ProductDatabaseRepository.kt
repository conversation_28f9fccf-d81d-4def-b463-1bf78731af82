package com.doorbit.projectconfigurator.projectconfigurator.repository

import com.doorbit.projectconfigurator.projectconfigurator.domain.ProductDatabaseId
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.ProductDatabase
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.cache.annotation.Caching
import org.springframework.context.event.EventListener
import org.springframework.core.env.Environment
import org.springframework.data.domain.Sort.Direction.ASC
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.index.Index
import org.springframework.data.mongodb.core.query.Criteria.where
import org.springframework.data.mongodb.core.query.Query.query
import org.springframework.data.mongodb.core.query.isEqualTo
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Repository
import org.springframework.web.server.ResponseStatusException

@Repository
class ProductDatabaseRepository(
    private val mongoTemplate: MongoTemplate,
    environment: Environment,
) : RepositoryBase(ProductDatabase::class.java.simpleName, environment) {

    /**
     * Insert a new product database into the repository.
     * Once inserted a product database is immutable.
     */
    @Caching(
        evict = [CacheEvict("databaseVersions", allEntries = true)],
        cacheable = [Cacheable("productDatabase", key = "#id")]
    )
    fun insert(id: ProductDatabaseId, db: ProductDatabase): ProductDatabase {
        mongoTemplate.insert(db, collectionName)
        return db
    }

    @Cacheable("productDatabase", key = "#id")
    fun findById(id: ProductDatabaseId): ProductDatabase {
        return mongoTemplate.findById(id, ProductDatabase::class.java, collectionName)!!
    }

    @Cacheable("moduleNames", key = "#databaseVersion")
    fun findDistinctModuleNames(databaseVersion: String): List<String> {
        return mongoTemplate.findDistinct(query(where("versionName").isEqualTo(databaseVersion)), "module.name", collectionName, String::class.java)
    }

    @Cacheable("databaseVersions")
    fun databaseVersions(): List<ProductDatabase> {
        return mongoTemplate.findAll(ProductDatabase::class.java, collectionName).distinct()
    }

    fun assertVersionNameIsUnique(versionName: String) {
        if (mongoTemplate.exists(query(where("versionName").isEqualTo(versionName)), collectionName)) {
            throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Product database with version name $versionName already exists")
        }
    }

    @CacheEvict("productDatabase", key = "#db.id")
    fun save(db: ProductDatabase) {
        mongoTemplate.save(db, collectionName)
    }

    @EventListener(ApplicationReadyEvent::class)
    fun createIndices() {
        val indexOps = mongoTemplate.indexOps(collectionName)
        indexOps.ensureIndex(Index().named("versionName").on("versionName", ASC))
    }

}
