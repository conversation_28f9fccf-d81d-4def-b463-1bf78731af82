<template>
  <v-data-table
    hide-no-data
    :headers="headers"
    :items="items"
    :items-per-page="-1"
    :sort-by="[
      {
        key: 'leistungsPositionSeq',
        order: 'asc'
      }
    ]"
    :group-by="[
      {
        key: 'moduleSeq',
        order: 'asc'
      },
      {
        key: 'vergabeEinheitSeq',
        order: 'asc'
      },
      {
        key: 'komponenteSeq',
        order: 'asc'
      },
      {
        key: 'leistungSeq',
        order: 'asc'
      }
    ]"
    :custom-filter="customFilterFn"
    :search="search"
    :cell-props="getRowProps"
  >
    <template #bottom>
      <!--hide footer. data-table-virtual (without footer) is buggy, do not use -->
    </template>
    <template #group-header="{ index, item, columns, toggleGroup, isGroupOpen }">
      <d-vergabe-einheiten-table-group-header-row
        :index="index"
        :item="item"
        :columns="columns"
        :toggle-group="toggleGroup"
        :is-group-open="isGroupOpen"
        @create-leistung="emits('createLeistung', $event)"
        @open-leistung="emits('openLeistung', $event)"
      />
    </template>
    <template v-if="displayContext === 'BACKOFFICE'" #item="{ item }">
      <tr class="cursor-pointer bg-grey-lighten-3" @click.stop="emits('openLeistung', item.leistungId!)">
        <td :style="'border-left: 5px solid ' + item.moduleColor">
          <div class="pl-16 d-flex align-center">
            <v-checkbox
              v-if="displayContext === 'BACKOFFICE'"
              flat
              color="success"
              hide-details
              density="compact"
              :model-value="item.leistungsPositionSelected"
              @click.stop
              @update:model-value="
                (selection) =>
                  onLeistungspositionSelection({
                    leistungsPositionId: item.leistungsPositionId!,
                    leistungId: item.leistungId!,
                    selection: selection as boolean
                  })
              "
            />
            <div class="ml-2 font-italic">
              <span>{{ item.leistungsPositionName }}</span>
            </div>
          </div>
        </td>
        <td v-if="displayContext === 'BACKOFFICE'">
          <div class="d-flex align-center justify-end">
            <span class="text-no-wrap mr-1">{{ item.leistungsPositionAmount }} {{ item.leistungsPositionUnit }}</span>
            <d-info v-if="item.leistungsPositionFormula">
              <div>{{ t('domain.calculationFormula') }}:</div>
              <div class="font-italic">{{ item.leistungsPositionFormula }}</div>
            </d-info>
          </div>
        </td>
        <td v-if="displayContext === 'BACKOFFICE'">
          <div class="d-flex justify-end">{{ currencyFormat(item.leistungsPositionUnitPrice, locale) }}</div>
        </td>
        <td v-if="displayContext === 'BACKOFFICE'">
          <div class="d-flex justify-end">{{ currencyFormat(item.leistungsPositionNetCosts, locale) }}</div>
        </td>
        <td v-if="displayContext === 'BACKOFFICE'">
          <div class="d-flex align-center justify-end">
            <span class="text-no-wrap mr-1">{{ currencyFormat(item.leistungsPositionSecuritySurcharge, locale) }}</span>
            <d-info v-if="item.leistungsPositionSecuritySurchargePercent">
              {{ (item.leistungsPositionSecuritySurchargePercent * 100).toLocaleString(locale) + '%' }}
            </d-info>
          </div>
        </td>
        <td v-if="displayContext === 'BACKOFFICE'">
          <div class="d-flex align-center justify-end">
            <span class="text-no-wrap mr-1">{{ currencyFormat(item.leistungsPositionMargin, locale) }}</span>
            <d-info v-if="item.leistungsPositionMarginPercent">
              {{ (item.leistungsPositionMarginPercent * 100).toLocaleString(locale) + '%' }}
            </d-info>
          </div>
        </td>
        <td v-if="displayContext === 'BACKOFFICE'">
          <div class="d-flex justify-end">{{ currencyFormat(item.leistungsPositionOfferPriceNet, locale) }}</div>
        </td>
        <td v-if="displayContext === 'BACKOFFICE'">
          <div class="d-flex align-center justify-end">
            <span class="text-no-wrap mr-1">{{ currencyFormat(item.leistungsPositionVatAmount, locale) }}</span>
            <d-info v-if="item.leistungsPositionVatRate">
              {{ (item.leistungsPositionVatRate * 100).toLocaleString(locale) + '%' }}
            </d-info>
          </div>
        </td>
        <td>
          <div class="d-flex justify-end">{{ currencyFormat(item.leistungsPositionOfferPriceGross, locale) }}</div>
        </td>
        <td />
        <td />
      </tr>
    </template>
  </v-data-table>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { DKomponenteHeader } from '@/model/DKomponenteHeader';
import DVergabeEinheitenTableGroupHeaderRow from '@/components/content/vergabe-einheiten-table/d-vergabe-einheiten-table-group-header-row.vue';
import { currencyFormat } from '@/utility/filter';
import { useI18n } from 'vue-i18n';
import { VergabeEinheitenTableItem } from '@/components/content/vergabe-einheiten-table/VergabeEinheitenTableItem';
import { normalizeStringGermanAndSpanish, searchFilter } from './searchFilter';
import {
  mapCompletenessToToken,
  mapSelectionToToken,
  mapWarningToToken
} from '@/components/content/vergabe-einheiten-table/filterToken';
import { getVergabeEinheitenTableItems } from './getVergabeEinheitenTableItems';
import { useConfigurationTableService } from '@/service/use-configuration-table-service';
import { LeistungCreationContext } from '@/components/content/LeistungCreationContext';
import {
  ConfigurationTable,
  useDLeistungspositionSelectionMutation,
  VergabeEinheit
} from '@/adapter/graphql/generated/graphql';
import { useProjectService } from '@/service/use-project-service';
import DInfo from '@/components/ui/d-info.vue';

const { locale, t } = useI18n();
const { selectedConfiguration, displayContext } = useProjectService();
const { configurationTable, setConfigTable, moduleToColorMap, moduleConfigs } = useConfigurationTableService();
const { mutate: selectLeistungsposition } = useDLeistungspositionSelectionMutation();

function getRowProps({ item, column }: { item: VergabeEinheitenTableItem; column: { key: string } }) {
  return {
    style: column.key === 'leistungsPositionName' ? `border-left: 5px solid ${item.moduleColor};` : ''
  };
}

const emits = defineEmits<{ createLeistung: [context: LeistungCreationContext]; openLeistung: [leistungId: string] }>();
defineProps<{ search: string }>();

const headers = computed<DKomponenteHeader[]>(() => [
  { sortable: false, title: t('domain.displayName'), key: 'leistungsPositionName' },
  ...(displayContext.value === 'END_CUSTOMER'
    ? [
        {
          sortable: false,
          title: t('domain.priceGross'),
          key: 'leistungsPositionOfferPriceGross',
          align: 'end' as never,
          filterable: false
        }
      ]
    : [
        {
          sortable: false,
          title: t('domain.amount'),
          key: 'leistungsPositionAmount',
          align: 'end' as never,
          filterable: false
        },
        {
          sortable: false,
          title: t('domain.unitPrice'),
          key: 'leistungsPositionUnitPrice',
          align: 'end' as never,
          filterable: false
        },
        {
          sortable: false,
          title: t('domain.netCosts'),
          key: 'leistungsPositionNetCosts',
          align: 'end' as never,
          filterable: false
        },
        {
          sortable: false,
          title: t('domain.securitySurcharge'),
          key: 'leistungsPositionSecuritySurcharge',
          align: 'end' as never,
          filterable: false
        },
        {
          sortable: false,
          title: t('domain.margin'),
          key: 'leistungsPositionMargin',
          align: 'end' as never,
          filterable: false
        },
        {
          sortable: false,
          title: t('domain.offerPriceNet'),
          key: 'leistungsPositionOfferPriceNet',
          align: 'end' as never,
          filterable: false
        },
        {
          sortable: false,
          title: t('domain.vatAmount'),
          key: 'leistungsPositionVatAmount',
          align: 'end' as never,
          filterable: false
        },
        {
          sortable: false,
          title: t('domain.offerPriceGross'),
          key: 'leistungsPositionOfferPriceGross',
          align: 'end' as never,
          filterable: false
        }
      ]),
  { sortable: false, title: '', key: 'data-table-group', filterable: false },
  { sortable: false, title: '', key: 'CREATE_NEW', align: 'end' as never, filterable: false }
]);

function customFilterFn(_: string, query: string, item?: { value: unknown; raw: VergabeEinheitenTableItem }): boolean {
  return searchFilter(query, [
    normalizeStringGermanAndSpanish(item?.raw.vergabeEinheitName || ''),
    normalizeStringGermanAndSpanish(item?.raw.komponenteName || ''),
    normalizeStringGermanAndSpanish(item?.raw.leistungName || ''),
    normalizeStringGermanAndSpanish(item?.raw.leistungsPositionName || ''),
    mapSelectionToToken(item?.raw.leistungSelected),
    mapCompletenessToToken(item?.raw.vergabeEinheitCompleteness),
    mapCompletenessToToken(item?.raw.komponenteCompleteness),
    mapWarningToToken(item?.raw.vergabeEinheitHasWarnings),
    mapWarningToToken(item?.raw.komponenteHasWarnings)
  ]);
}

const activeModuleNames = computed(() =>
  moduleConfigs.value.filter((config) => config.isActive).map((config) => config.name)
);
const items = computed(() =>
  getVergabeEinheitenTableItems(
    (configurationTable.value!.vergabeEinheiten as VergabeEinheit[]).filter((ve) =>
      activeModuleNames.value.includes(ve.moduleName)
    ),
    locale.value,
    moduleToColorMap.value
  )
);

async function onLeistungspositionSelection({
  leistungsPositionId,
  selection,
  leistungId
}: {
  leistungsPositionId: string;
  leistungId: string;
  selection: boolean;
}) {
  const result = await selectLeistungsposition({
    configurationId: selectedConfiguration.value!.configurationId,
    leistungId,
    leistungsPositionId,
    selection
  });
  if (result?.data) {
    setConfigTable(result.data.selectLeistungsPosition as ConfigurationTable);
  }
}
</script>

<style scoped>
:deep(thead) {
  background-color: white;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
:deep(tbody .v-data-table__tr) {
  background-color: #eeeeee !important;
  color: #000000 !important;
}
:deep(.v-table__wrapper) {
  overflow: unset !important;
}
</style>
