logging:
  level:
    root: INFO
    org.springframework: INFO
    com.doorbit: TRACE

spring:
  data:
    mongodb:
      uri: **********************************************************
      database: projectconfigurator
#  security:
#    oauth2:
#      resourceserver:
#        jwt:
#          jwk-set-uri: https://auth-integ.doorbit.com/login/realms/doorbit/protocol/openid-connect/certs
  jpa:
    show-sql: false
    properties:
      hibernate.format_sql: false

  graphql:
    graphiql:
      enabled: true
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
    tools:
      introspection-enabled: true

management:

  prometheus:
    metrics:
      export:
        enabled: false

  logging:
    metrics:
      export:
        # Enabling this toggle will log metrics to the console every 15s (hardcoded in MicrometerConfig)
        enabled: false