import { NestableTableElement } from '@/model/NestableTableElement';

export function getSortRawFn(colName: string, getParent: (parentName: string) => NestableTableElement) {
  return (a: NestableTableElement, b: NestableTableElement) => {
    if ((a.isRoot && b.isRoot) || (!a.isRoot && !b.isRoot && a.parent === b.parent)) {
      return (a[colName] as string).localeCompare(b[colName] as string);
    } else {
      return (getParent(a.parent)[colName] as string).localeCompare(getParent(b.parent)[colName] as string);
    }
  };
}
