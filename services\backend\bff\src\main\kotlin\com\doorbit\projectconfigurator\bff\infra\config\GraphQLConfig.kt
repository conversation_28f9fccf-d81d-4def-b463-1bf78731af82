package com.doorbit.projectconfigurator.bff.infra.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.graphql.execution.RuntimeWiringConfigurer

@Configuration
class GraphQLConfig {
    @Bean
    fun scalarsAndTypes(): RuntimeWiringConfigurer {
        return RuntimeWiringConfigurer { builder ->
        }
    }
}
