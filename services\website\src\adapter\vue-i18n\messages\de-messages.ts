import { DefineLocaleMessage } from 'vue-i18n';

const deMessages: DefineLocaleMessage = {
  $vuetify: {
    input: {
      clear: 'Löschen'
    },
    carousel: {
      prev: 'Vorheriges Bild',
      next: 'Nächstes Bild',
      ariaLabel: {
        delimiter: '-'
      }
    },
    open: '<PERSON><PERSON><PERSON>',
    close: 'Schließen',
    badge: 'We dont need no stinkin badges!',
    dataFooter: {
      pageText: 'Seite',
      itemsPerPageText: 'Elemente pro Seite'
    },
    noDataText: 'Keine Daten'
  },
  errorViews: {
    notFound: '404 - Seite nicht gefunden',
    genericError: 'Ups - etwas ist schiefgelaufen ...',
    apolloError: 'Server Fehler',
    reload: 'Neu Laden',
    consultAdmin: 'Bitte kontaktieren Sie den Administrator',
    missingListingId: 'Listing-ID fehlt in der URL',
    projectDataNotLoaded: 'Projekt-Daten konnten nicht geladen werden',
    serverErrorLeistungCreate: 'Server-Error, konnte Leistung nicht anlegen',
    noMassedaten: 'Keine Massedaten verfügbar für die Listing-ID'
  },
  domain: {
    massedaten: 'Massedaten',
    preSelection: 'Schnell-Konfiguration',
    notes: 'Notizen',
    vergabeEinheitenFilters: {
      searchLabel: 'Suche',
      filterLabel: 'Filter',
      filterItems: {
        selected: 'Ausgewählte',
        invalid: 'Fehlerhafte',
        incomplete: 'Unvollständige',
        configured: 'Konfiguriert',
        partiallyConfigured: 'Teilw. konfiguriert',
        notConfigured: 'Nicht konfiguriert'
      }
    },
    costOverview: 'Kostenübersicht',
    globallyAvailable: {
      globallyAvailable: 'Verfügbar in allen Konfigurationen',
      notGloballyAvailable: 'Nur in aktueller Konfiguration verfügbar'
    },
    displayName: 'Kurztext',
    amount: 'Menge',
    priceGross: 'Preis (Brutto)',
    unitPrice: 'Einheits-Preis',
    securitySurcharge: 'Sicherheits-Aufschlag',
    margin: 'Marge',
    offerPriceNet: 'Sanierungspreis (Netto)',
    vatAmount: 'USt.',
    vatRate: 'Steuer-Satz',
    netCosts: 'Vergabebudget',
    totalProjectCosts: 'Sanierungspreis (Brutto)',
    offerPriceGross: 'Endkunden-Preis (Brutto)',
    calculationFormula: 'Berechnungsformel',
    contractorName: 'Renaldo',
    dataSource: {
      dataSource: 'Datenquelle',
      created: 'Neu Angelegt',
      imported: 'Importiert/AirTable'
    },
    selfService: 'Eigenleistung',
    eligible: 'Förderfähig',
    notEligible: 'Nicht Förderfähig',
    dataSheet: 'Datenblatt',
    execution: 'Ausführung',
    vergabeEinheit: {
      component: 'Komponente',
      grossQuotePrice: 'Angebotspreis Brutto',
      netPlanningCosts: 'Plankosten Netto'
    },
    leistung: {
      selected: 'Leistung ausgewählt',
      notSelected: 'Leistung unausgewählt',
      newLeistung: 'Neue Leistung anlegen',
      leistung: 'Leistung',
      uValue: 'U-Wert',
      manufacturer: 'Hersteller',
      offerPriceGross: 'Angebots-Preis (Brutto)',
      offerPrice: 'Angebots-Preis',
      leistungType: 'Leistungstyp',
      unit: 'Einheit',
      types: {
        ZERO_VARIANT: 'Null-Variante',
        ALTERNATIVE: 'Alternative',
        ADD_ON: 'Add-On'
      }
    },
    leistungsposition: {
      takeFromMassedaten: 'Klicken um als Menge zu übernehmen',
      creationInstruction: 'Alle Attribute sind optional. Für leere Felder werden Standardwerte gesetzt.',
      unitCreateOrSelect: 'Einheit (anlegen or auswählen)',
      leistungspositionen: 'Leistungspositionen',
      newLeistungsposition: 'Neue Leistungsposition anlegen',
      description: 'Beschreibung',
      unitPrice: 'Einheitspreis',
      dinNumber: 'DIN-Nummer'
    },
    configurations: {
      archiveConfirm: 'Wollen Sie die Konfiguration wirklich löschen?',
      createNewConfig: 'Neue Konfiguration anlegen',
      createNewError: 'Konnte Konfiguration nicht erstellen',
      databaseVersion: 'Datenbank-Version',
      newDatabaseVersion: 'Neue Datenbank-Version anlegen',
      configLabel: 'Bezeichnung',
      noneCreated: 'Noch keine Konfigurationen erstellt',
      createFirst: 'Erstellen Sie Ihre erste Konfiguration, indem Sie oben auf + klicken',
      noDbVersions: 'Keine DB-Versionen verfügbar'
    }
  },
  common: {
    ok: 'OK',
    yes: 'Ja',
    no: 'Nein',
    new: 'Neu',
    save: 'Speichern',
    cancel: 'Abbrechen',
    delete: 'Löschen',
    prev: 'Vorherige',
    next: 'Nächste',
    download: 'Herunterladen',
    redirectMessage: 'Du wirst in Kürze weitergeleitet …',
    pleaseWait: 'Bitte Warten',
    back: 'Zurück'
  },
  formRules: {
    required: 'Angabe fehlt'
  }
};

export default deMessages;
