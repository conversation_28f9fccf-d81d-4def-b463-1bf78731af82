<template>
  <tr
    :class="{
      [`bg-grey-lighten-${7 - item.depth}`]: item.depth > 1,
      'cursor-pointer': canToggle(item) && !!childrenAmount,
      'd-none': item.depth === 0
    }"
    @click="canToggle(item) && childrenAmount && toggleGroup(item)"
  >
    <td v-if="!raw" :colspan="columns.length">
      <div>{{ t('errorViews.genericError') }}</div>
    </td>
    <template v-else>
      <td
        :style="'border-left: 5px solid ' + raw.moduleColor"
        :class="{
          'pl-0': item.depth === 1,
          'pl-3': item.depth === 2,
          'pl-6': item.depth === 3 && canToggle(item),
          'pl-16': item.depth === 3 && !canToggle(item)
        }"
      >
        <div class="d-flex align-center">
          <v-btn
            v-show="canToggle(item)"
            :disabled="!childrenAmount"
            :icon="isGroupOpen(item) ? '$expand' : '$next'"
            size="small"
            variant="text"
          />
          <v-checkbox
            v-if="item.depth > 2 && displayContext === 'BACKOFFICE'"
            flat
            color="success"
            hide-details
            density="compact"
            :model-value="raw.leistungSelected ?? false"
            @click.stop
            @update:model-value="(val) => onSelectionChanged(val, raw!.leistungId)"
          />
          <span
            class="pa-2"
            :class="[
              { 'text-h5 font-weight-medium ml-2': item.depth === 1 },
              { 'text-h6  ml-2': item.depth === 2 },
              { 'font-weight-medium': item.depth === 3 }
            ]"
            >{{ rowValues.name }}</span
          >
          <span class="ml-2">({{ childrenAmount }})</span>
        </div>
      </td>
      <template v-if="displayContext === 'BACKOFFICE'">
        <td />
        <td />
        <td>
          <div class="d-flex justify-end">{{ currencyFormat(rowValues.netCosts, locale) }}</div>
        </td>
        <td>
          <div class="d-flex justify-end">{{ currencyFormat(rowValues.securitySurcharge, locale) }}</div>
        </td>
        <td>
          <div class="d-flex justify-end">{{ currencyFormat(rowValues.margin, locale) }}</div>
        </td>
        <td>
          <div class="d-flex justify-end">{{ currencyFormat(rowValues.offerPriceNet, locale) }}</div>
        </td>
        <td>
          <div class="d-flex justify-end">{{ currencyFormat(rowValues.vatAmount, locale) }}</div>
        </td>
      </template>
      <td>
        <div class="d-flex justify-end">{{ currencyFormat(rowValues.offerPriceGross, locale) }}</div>
      </td>
      <td />
      <td>
        <div class="d-flex justify-end align-center">
          <div v-if="item.depth === 3" class="d-flex align-center ml-4">
            <div class="d-flex align-center mr-2"></div>
            <div class="d-flex align-center"></div>
          </div>
          <v-btn
            v-if="item.depth === 2 && displayContext === 'BACKOFFICE'"
            flat
            color="black"
            density="comfortable"
            variant="tonal"
            :text="t('common.new')"
            :prepend-icon="mdiPlus"
            @click.stop="
              emits('createLeistung', {
                komponenteId: raw.komponenteId!,
                komponenteName: raw.komponenteName,
                vergabeEinheitName: raw.vergabeEinheitName
              })
            "
          />
          <v-btn
            v-if="item.depth === 3 && displayContext === 'BACKOFFICE'"
            flat
            class="mx-2"
            color="black"
            density="comfortable"
            variant="text"
            :icon="mdiPencil"
            @click.stop="emits('openLeistung', raw.leistungId!)"
          />
        </div>
      </td>
    </template>
  </tr>
</template>
<script setup lang="ts">
import { currencyFormat } from '@/utility/filter';
import { useI18n } from 'vue-i18n';
import { InternalDataTableHeader, TableItemGroup } from '@/components/content/vergabe-einheiten-table/TableItemGroup';
import { VergabeEinheitenTableItem } from '@/components/content/vergabe-einheiten-table/VergabeEinheitenTableItem';
import { computed, onMounted, watch } from 'vue';
import { mdiPencil, mdiPlus } from '@mdi/js';
import { useProjectService } from '@/service/use-project-service';
import { useConfigurationTableService } from '@/service/use-configuration-table-service';
import { LeistungCreationContext } from '@/components/content/LeistungCreationContext';
import { getRawVergabeEinheitenTableItem } from '@/components/content/vergabe-einheiten-table/getRawVergabeEinheitenTableItem';

const { locale, t } = useI18n();
const { displayContext, selectedConfiguration } = useProjectService();
const { changeLeistungSelection, collapse } = useConfigurationTableService();

const emits = defineEmits<{ createLeistung: [context: LeistungCreationContext]; openLeistung: [leistungId: string] }>();
const props = defineProps<{
  index: number;
  item: TableItemGroup;
  raw?: VergabeEinheitenTableItem;
  columns: InternalDataTableHeader<TableItemGroup>[];
  isGroupOpen(item: TableItemGroup): boolean;
  toggleGroup(item: TableItemGroup): void;
}>();

onMounted(() => {
  if (props.item.depth === 0 && !props.isGroupOpen(props.item)) {
    props.toggleGroup(props.item);
  }
});

watch(collapse, (val) => {
  if (props.item.depth !== 0 && val && props.isGroupOpen(props.item)) {
    props.toggleGroup(props.item);
  }
});

function canToggle(item: TableItemGroup) {
  if (item.depth === 3) {
    return displayContext.value === 'BACKOFFICE';
  }
  return item.depth > 0 && !!childrenAmount.value;
}

const childrenAmount = computed<number>(
  () =>
    props.item.items.filter(
      (subItem) => subItem.value || (subItem.type === 'item' && subItem.raw?.leistungsPositionName)
    ).length
);
const raw = computed(() => getRawVergabeEinheitenTableItem(props.item));
const rowValues = computed(() => {
  const emptyResponse = {
    name: undefined,
    netCosts: undefined,
    offerPriceNet: undefined,
    offerPriceGross: undefined,
    margin: undefined,
    vatAmount: undefined,
    securitySurcharge: undefined
  };
  if (!raw.value) return emptyResponse;
  switch (props.item.depth) {
    case 1:
      return {
        name: raw.value.vergabeEinheitName,
        netCosts: raw.value.vergabeEinheitNetCosts,
        offerPriceNet: raw.value.vergabeEinheitOfferPriceNet,
        offerPriceGross: raw.value.vergabeEinheitOfferPriceGross,
        margin: raw.value.vergabeEinheitMargin,
        vatAmount: raw.value.vergabeEinheitVatAmount,
        securitySurcharge: raw.value.vergabeEinheitSecuritySurcharge
      };
    case 2:
      return {
        name: raw.value.komponenteName,
        netCosts: raw.value.komponenteNetCosts,
        offerPriceNet: raw.value.vergabeEinheitOfferPriceNet,
        offerPriceGross: raw.value.komponenteOfferPriceGross,
        margin: raw.value.komponenteMargin,
        vatAmount: raw.value.komponenteVatAmount,
        securitySurcharge: raw.value.komponenteSecuritySurcharge
      };
    case 3:
      return {
        name: raw.value.leistungName,
        netCosts: raw.value.leistungNetCosts,
        offerPriceNet: raw.value.vergabeEinheitOfferPriceNet,
        offerPriceGross: raw.value.leistungOfferPriceGross,
        margin: raw.value.leistungMargin,
        vatAmount: raw.value.leistungVatAmount,
        securitySurcharge: raw.value.leistungSecuritySurcharge
      };
    default:
      return emptyResponse;
  }
});

async function onSelectionChanged(value: boolean | null, leistungId?: string) {
  if (!leistungId) return;
  await changeLeistungSelection({
    configurationId: selectedConfiguration.value!.configurationId,
    leistungId,
    selection: !!value
  });
}
</script>
