{"name": "website", "version": "0.1.0", "private": true, "scripts": {"dev": "VITE_BUILD_GIT_SHORT_SHA=$(git show -s --format='%h') VITE_BUILD_DATE=$(date -Iseconds) vite --host --mode dev", "dev-windows": "vite --host --mode dev", "build": "vue-tsc --noEmit && printenv && vite build --mode integ", "build-live": "vue-tsc --noEmit && printenv && vite build --mode live", "preview": "vite preview", "lint": "eslint . --fix --ignore-path .gitignore", "prettier": "prettier --write ."}, "dependencies": {"@apollo/client": "==3.10.1", "@fontsource/montserrat": "==5.0.18", "@fontsource/roboto": "==5.0.13", "@mdi/js": "==7.4.47", "@mdi/svg": "==7.4.47", "@vue/apollo-composable": "==4.0.2", "@vue/apollo-option": "==4.0.0", "@vueuse/core": "==10.9.0", "@vueuse/router": "==10.9.0", "graphql": "==16.8.1", "graphql-ws": "==5.16.0", "keycloak-js": "==23.0.5", "marked": "^13.0.1", "uuid": "==9.0.1", "vue": "==3.4.26", "vue-i18n": "==9.13.1", "vue-router": "==4.3.2", "vuetify": "==3.5.17"}, "devDependencies": {"@babel/core": "==7.24.5", "@graphql-codegen/cli": "==5.0.2", "@intlify/unplugin-vue-i18n": "==4.0.0", "@rollup/pluginutils": "==5.1.0", "@rushstack/eslint-patch": "==1.10.3", "@vitejs/plugin-vue": "==5.0.4", "@vue/eslint-config-prettier": "==9.0.0", "@vue/eslint-config-typescript": "==13.0.0", "eslint": "==8.57.0", "eslint-plugin-vue": "==9.25.0", "prettier": "==3.2.5", "sass": "==1.75.0", "typescript": "==5.4.5", "vite": "==5.2.10", "vite-plugin-vuetify": "==2.0.3", "vue-tsc": "==2.0.15"}}