package com.doorbit.projectconfigurator.bff.infra.config

import com.doorbit.projectconfigurator.bff.core.domain.extension.WithLogger
import com.doorbit.projectconfigurator.bff.core.domain.extension.e
import com.doorbit.projectconfigurator.bff.infra.config.http.ApplicationProperties
import com.doorbit.projectconfigurator.bff.infra.config.http.CacheDetailConfig
import com.github.benmanes.caffeine.cache.Caffeine
import io.micrometer.core.instrument.Metrics
import java.util.concurrent.TimeUnit
import org.springframework.cache.Cache
import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.CachingConfigurer
import org.springframework.cache.caffeine.CaffeineCache
import org.springframework.cache.interceptor.CacheErrorHandler
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class CacheConfig(
    private val applicationProperties: ApplicationProperties,
) : CachingConfigurer {

    private companion object : WithLogger()

    @Bean
    override fun cacheManager(): CacheManager {
        val defaultCacheName = "default"
        val defaultCache = buildCache(defaultCacheName, applicationProperties.cache["default"]!!)
        val customCaches = applicationProperties.cache.keys.filterNot { it == "default" }.map {
            buildCache(it, applicationProperties.cache[it]!!)
        }

        val cacheManager = DoorbitSimpleCacheManager { cacheName ->
            buildCache(cacheName, applicationProperties.cache["default"]!!)
        }

        cacheManager.setCaches(listOf(defaultCache, *customCaches.toTypedArray()))
        return cacheManager
    }

    private fun buildCache(cacheName: String, cacheConfig: CacheDetailConfig): CaffeineCache {
        return CaffeineCache(
            cacheName, Caffeine.newBuilder()
                .expireAfterWrite(cacheConfig.ttlInMinutes, TimeUnit.MINUTES)
                .maximumSize(cacheConfig.maxSize)
                .build()
        )
    }

    /**
     * Please read below!
     */
    override fun errorHandler(): CacheErrorHandler {
        return SuppressingExceptionsCacheErrorHandler()
    }

    /**
     * Suppresses exceptions from cache operations and logs them.
     * Using this class instead of the default error handler is tremendously important, as otherwise
     * the application will be basically "down" if the cache is not available by throwing Exceptions to the
     * API client.
     * By suppressing them, the application will fetch the data from the originating backends instead.
     * This could itself lead to a downtime of the backends for a couple of minutes due to the increased load and scale up time,
     * however it does not mean a complete, permanent downtime of the application.
     */
    class SuppressingExceptionsCacheErrorHandler : CacheErrorHandler {
        override fun handleCacheGetError(exception: RuntimeException, cacheName: Cache, key: Any) {
            Metrics.counter("cache.errors", "get", cacheName.name).increment()
            LOGGER.e(exception) { "Cache error on cache-get for cache $cacheName for key $key: ${exception.message}" }
        }

        override fun handleCachePutError(exception: RuntimeException, cacheName: Cache, key: Any, value: Any?) {
            Metrics.counter("cache.errors", "put", cacheName.name).increment()
            LOGGER.e(exception) { "Cache error on cache-put for cache $cacheName for key $key: ${exception.message}" }
        }

        override fun handleCacheEvictError(exception: RuntimeException, cacheName: Cache, key: Any) {
            Metrics.counter("cache.errors", "evict", cacheName.name).increment()
            LOGGER.e(exception) { "Cache error on cache-evict for cache $cacheName for key $key: ${exception.message}" }
        }

        override fun handleCacheClearError(exception: RuntimeException, cacheName: Cache) {
            Metrics.counter("cache.errors", "clear", cacheName.name).increment()
            LOGGER.e(exception) { "Cache error on cache-clear for cache $cacheName: ${exception.message}" }
        }
    }

}