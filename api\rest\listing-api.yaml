openapi: 3.0.3
info:
  title: Listing API
  description: |-
  version: 0.0.1
servers:
  - url: http://localhost:8081
paths:
  /listings:
    post:
      tags:
        - listings
      summary: Legt Inserate an.
      operationId: createListing
      parameters:
        - $ref: "#/components/parameters/header.Testing"
        - $ref: "#/components/parameters/header.CreateDemo"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              description: |-
                - Request Payload zur Anlage eines Inserates.
              required:
                - flow_id
                - user_id
                - billing_user_id
              properties:
                offline_listing_id:
                  type: string
                flow_id:
                  type: string
                user_id:
                  $ref: "#/components/schemas/UserId"
                billing_user_id:
                  $ref: "#/components/schemas/UserId"
                billing_group_id:
                  $ref: "#/components/schemas/BillingGroupId"
      responses:
        "201":
          description: Neues Inserat wie unten stehend angelegt.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Listing"
    get:
      tags:
        - listings
      summary: Listings suchen. Gibt nur Ids zurück.
      description: |-
        Es ist möglich wie folgt Inserate abzufragen und eine Kombinierte Abfrage wird ODER verknüpft:

         - Anhand einer Liste von ListingIds
         - Anhand einer UserId
         - Anhand einer Liste von BillingGroupIds
         - Mittels Pagination
      operationId: searchListings
      parameters:
        - $ref: "#/components/parameters/query.ListingIds"
        - $ref: "#/components/parameters/query.UserId"
        - $ref: "#/components/parameters/query.FlowId"
        - $ref: "#/components/parameters/query.GroupIdList"
        - $ref: "#/components/parameters/query.PaginationId"
        - $ref: "#/components/parameters/query.Limit"
      responses:
        "200":
          description: Der Request war valide.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListingSearchResult"
    delete:
      tags:
        - listings
      summary: Delete listings of a user.
      operationId: deleteListingsOfUser
      parameters:
        - $ref: "#/components/parameters/header.UserIdRequired"
      responses:
        "204":
          description: Delete listings of a user.
        "404":
          description: For the given user id no listings were found.

  /listings/{listing-id}/sync-availability:
    get:
      tags:
        - listings
      summary: Get the sync availability of a listing.
      operationId: getSyncAvailability
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/query.SyncDestination"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListingSyncAvailability"
        "404":
          description: For the given listing id no listing was found.

  /listings/{listing-id}/copy-to-another-user:
    post:
      tags:
        - listings
      summary: Copy a listing to another user.
      operationId: copyListingToAnotherUser
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/query.UserIdRequired"
        - $ref: "#/components/parameters/query.GroupIdOptional"
      responses:
        "200":
          description: Listing copied successfully.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EnrichedListing"

  /listings/{listing-id}/fields:
    put:
      tags:
        - fields
      summary: Ein oder mehrere Felder des Inserats setzen.
      description: |-
        - Es ist möglich, das Inserat unabhängig von der Gestaltung des Frontends schrittweise zu befüllen.
        - Welches Datenmodell zum Tragen kommt, kann über den Endpunkt /field-models bezogen werden.
        - Die Sinnhaftigkeit der Befüllung des Modells wird mit so einem Modell dem Aufrufer übertragen. Zur Validierung kann der Endpunkt `/fields/validate` verwendet werden.
        - Bspw. ist die Kombination von "ListingType: Apartment zur Miete und der Angabe der Grundstücksfläche nicht sinnvoll, da diese Angabe nur bei Häusern Sinn macht und die 
          Suche auch keine Möglichkeit bietet, nach Grundstücksfläche für Wohnungen zu suchen.
      operationId: setFieldData
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FieldData"
      responses:
        "200":
          description: Felder erfolgreich gesetzt.
  /listings/{listing-id}/count-billable-feature:
    post:
      tags:
        - listings
      summary: Counts a billable feature.
      operationId: countBillableFeature
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CountBillableFeature"
      responses:
        "200":
          description: OK

  /listings/{listing-id}/field-completeness:
    get:
      tags:
        - fields
      summary: Gibt Informationen zu Feldern, ob diese gepflegt wurden oder nicht.
      operationId: getListingFieldCompleteness
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListingCompleteness"

  /listings/{listing-id}/fields/validate:
    post:
      tags:
        - fields
      summary: Validiert die Felder des Inserats.
      operationId: validatefields
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: Erfolgreich validiert. Das bedeutet nicht dass alles OK ist. ValidationResult siehe unten.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FieldDataValidationResult"
  /listings/{listing-id}/field-suggestions:
    get:
      tags:
        - fields
      summary: Gibt Vorschläge für die Felder eines Inserats zurück.
      description: |-
        Gibt Vorschläge für die Felder eines Inserats zurück. Feldvorschläge können auf Basis verschiedener Methodiken 
        gemacht werden, z. B. Bildauswertungen, OpenAI usw.
      operationId: getFieldSuggestions
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FieldSuggestions"

  /listings/{listing-id}/images/{image-id}/virtual-staging:
    post:
      tags:
        - listings
      summary: Ein Inseratfoto virtuell möblieren.
      operationId: virtualStaging
      parameters:
        - $ref: '#/components/parameters/path.ListingIdRequired'
        - $ref: '#/components/parameters/path.ImageIdRequired'
        - $ref: '#/components/parameters/query.EnableDecluttering'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VirtualStagingRequest'
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                type: object
                properties:
                  render_id:
                    type: string
        '404':
          description: Foto nicht gefunden.
  /listings/{listing-id}/images/{image-id}/virtual-staging/status:
    get:
      tags:
        - listings
      summary: Status des virtuellen Stagings abfragen.
      operationId: getVirtualStagingStatus
      parameters:
        - $ref: '#/components/parameters/path.ListingIdRequired'
        - $ref: '#/components/parameters/path.ImageIdRequired'
        - $ref: '#/components/parameters/query.RenderIdOptional'
      responses:
        '200':
          description: Ok
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VirtualStagingStatus'
        '404':
          description: Foto nicht gefunden.

  /listings/{listing-id}/building-data/default-construction:
    post:
      tags:
        - Building
      summary: Erzeugt und setzt aufs Listing ein 3D Gebäude aus dem Payload.
      description: |-
        - Das Gebäude wird lediglich aus den Werten des Payloads erzeugt und entspricht einem quadratischen Grundriss.
        - Ein möglicherweise vorhandenes Default-Modell wird (vorerst) überschrieben.
      operationId: constructBuildingData
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BuildingDefaultConstructionInput"
      responses:
        "200":
          description: Building erfolgreich konstruiert
          content:
            text/plain:
              schema:
                type: string

  /listings/{listing-id}/building:
    put:
      tags:
        - Building
      summary: 3D Daten des Gebäudes auf das Listing schreiben.
      description: Ein möglicherweise vorhandenes Default-Modell wird überschrieben.
      operationId: setBuilding
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Building"
      responses:
        "200":
          description: Building data erfolgreich hochgeladen.
    delete:
      tags:
        - Building
      summary: Build data vom Inserat löschen.
      operationId: deleteBuilding
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: Building data erfolgreich gelöscht.

  /listings/{listing-id}/building/merge-buildings:
    post:
      tags:
        - Building
      summary: Merge two buildings.
      operationId: mergeBuildings
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/query.OtherListingIdRequired"
        - $ref: "#/components/parameters/query.CreateNewListing"
        - $ref: "#/components/parameters/query.StoreyNumberToBeginWith"
      responses:
        "200":
          description: Building erfolgreich gemerged.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EnrichedListing"

  /listings/{listing-id}/building/floor-height-offset:
    put:
      tags:
        - Building
      summary: Setzt die Höhe eines Stockwerks.
      operationId: setFloorHeightOffset
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/query.FloorIdRequired"
        - $ref: "#/components/parameters/query.FloorHeightOffset"
      responses:
        "200":
          description: Floor height offset erfolgreich gesetzt.


  /listings/{listing-id}/building/custom-data:
    put:
      tags:
        - Building
      operationId: setBuildingCustomData
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/query.ConstructionPartIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FieldData"
      responses:
        "200":
          description: Custom building data erfolgreich hochgeladen.

  /listings/{listing-id}/scanned-building:
    put:
      tags:
        - Building
      summary: 3D Scan zwischenspeichern
      operationId: setScannedBuilding
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ScannedBuilding"

      responses:
        "200":
          description: Building data erfolgreich hochgeladen.
          content:
            text/plain:
              schema:
                type: string
    get:
      tags:
        - Building
      summary: 3D Scan abfragen
      operationId: getScannedBuilding
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScannedBuildingGet"
    delete:
      tags:
        - Building
      summary: Build data vom Inserat löschen.
      operationId: deleteBuildingScan
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: Building data erfolgreich gelöscht.
        "404":
          description: Für die angegebene InseratId wurde kein Inserat gefunden.

  /listings/{listing-id}/scanned-building/complete:
    post:
      tags:
        - Building
      summary: Scanned Building als completed markieren
      operationId: setScannedBuildingCompleted
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: Building data erfolgreich hochgeladen.

  /listings/{listing-id}/scanned-building/{construction-part-id}/custom-data:
    put:
      tags:
        - Building
      summary: Custom scanned building data schreiben
      operationId: setCustomScannedBuildingData
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/path.ConstructionPartIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FieldData"
      responses:
        "200":
          description: Custom building data erfolgreich hochgeladen.

  /listings/{listing-id}/scanned-building-data-history:
    get:
      tags:
        - Building
      summary: Get the building data history of a listing.
      operationId: getScannedBuildingHistory
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "#/components/schemas/ScannedBuildingDataEntry"

  /listings/{listing-id}/scanned-building-data-history/{building-data-id}:
    get:
      tags:
        - Building
      summary: Get the building data history of a listing.
      operationId: getBuildingDataHistoryItem
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/path.BuildingDataIdRequired"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScannedBuildingGet"

  /listings/{listing-id}/building/masses:
    get:
      tags:
        - building
      summary: Gebäudemassedaten abfragen
      operationId: getListingMassesById
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BuildingMasses"
                nullable: true

  /listings/{listing-id}:
    get:
      tags:
        - listings
      summary: Ein Inserat abfragen.
      operationId: getListingById
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EnrichedListing"
    delete:
      tags:
        - listings
      summary: Ein Inserat löschen.
      operationId: deleteListing
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "204":
          description: Inserat gelöscht.
        "404":
          description: Für die angegebene InseratId wurde kein Inserat gefunden.
  /listings/{listing-id}/publish-for-review:
    post:
      tags:
        - listings
      summary: Ein Listing publishen.
      operationId: publishForReview
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: Erfolg
        "400":
          description: Listing invalide. Bitte den validate Endpunkt aufrufen für mehr Infos.
        "404":
          description: Inserat inzwischen bereits gelöscht.
  /listings/{listing-id}/visibility:
    put:
      tags:
        - listings
      summary: Die Sichtbarkeit eines Inserates ändern.
      operationId: setListingVisibility
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/query.ListingVisibilityRequired"
        - $ref: "#/components/parameters/query.ListingPasswordOptional"
      responses:
        "200":
          description: Erfolg
  /listings/{listing-id}/third-party-synchronisations:
    put:
      tags:
        - listings
      summary: Setzt für eine 3rd party Sync Destination das Flag, ob die Synchronisierung an- oder abgeschaltet sein soll.
      operationId: setSynchronizationFlags
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - sync_destination
                - enabled
              properties:
                sync_destination:
                  type: string
                  description: See ListingSyncDestination.kt
                enabled:
                  type: boolean
      responses:
        "200":
          description: Erfolg
          content:
            application/json:
              schema:
                type: object
                properties:
                  synced_listing_url:
                    type: string
                    description: |-
                      - Die Url, die das Listing bei der 3rd party hat.
                      - Nur gesetzt, wenn die Synchronisierung aktiviert ist.
  /listings/review:
    get:
      tags:
        - listings
      summary: Den Review Status eines Inserats setzen. GET wird noch auf POST geändert.
      operationId: reviewListing
      parameters:
        - $ref: "#/components/parameters/query.ListingIdShortRequired"
        - $ref: "#/components/parameters/query.ActionRequired"
        - $ref: "#/components/parameters/query.ReasonOptional"
      responses:
        "204":
          description: Status erfolgreich gesetzt.
        "404":
          description: Inserat inzwischen bereits gelöscht.
        "409":
          description: Inserat bereits im Review (Da war jemand schneller)
  /listings/{listing-id}/statistics:
    get:
      tags:
        - listingStatistics
      summary: Die Statistiken eines Inserats abfragen.
      operationId: getListingStatistics
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListingStatistics"
  /listings/{listing-id}/statistics/view-count:
    post:
      tags:
        - listingStatistics
      summary: Ein Listing View zählen.
      operationId: countListingView
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: OK

  /listings/events/{event-type}:
    post:
      tags:
        - domainEvents
      summary: Endpunkt für das Senden von Listing spezifischen Domain Events.
      operationId: domainEventReceived
      parameters:
        - $ref: "#/components/parameters/path.EventType"
      requestBody:
        required: false
        description: Optionale Payload.
        content:
          application/json:
            schema:
              anyOf:
                - $ref: "#/components/schemas/ListingPublished"
      responses:
        "202":
          description: Event erfolgreich gespeichert. Event wird asynchron prozessiert.

  /listings/valuation:
    post:
      tags:
        - listings
      summary: |-
        Gibt eine Schätzung des Preises für die Inserate zurück.
        Die Schätzungen basieren auf dem aktuellen Stand der Inserate.
      operationId: getListingValuation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PropertyValuationRequest'
      responses:
        '200':
          description: Successful property valuation retrieval
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PropertyValuationResponse'
        '400':
          description: Invalid request
        '500':
          description: Internal Server Error
  /shared-listings/{listing-id}/sharing-settings:
    post:
      tags:
        - Sharing and collaboration
      summary: Fügt Shares hinzu oder updated bestimmte Shares.
      operationId: addSharingSettings
      description: Endpunkt ist idempotent. Existiert ein Share bereits wird es geupdated.
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/query.FlowIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SharingSettings"
      responses:
        "200":
          description: Erfolg
    delete:
      tags:
        - Sharing and collaboration
      summary: Löscht alle SharingSettings.
      operationId: deleteSharingSettings
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: Erfolg
    put:
      tags:
        - Sharing and collaboration
      summary: Setzt die Sharing Einstellungen für ein Inserat.
      operationId: setSharingSettings
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
        - $ref: "#/components/parameters/query.FlowIdRequired"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SharingSettings"
      responses:
        "200":
          description: Erfolg
    get:
      tags:
        - Sharing and collaboration
      summary: Holt die Sharing Einstellungen für ein Inserat.
      operationId: getSharingSettings
      parameters:
        - $ref: "#/components/parameters/path.ListingIdRequired"
      responses:
        "200":
          description: Erfolg
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SharingSettings"
  /shared-listings/shared-with:
    get:
      tags:
        - Sharing and collaboration
      summary: Gibt ListingIds zurück, die mit einem User oder einer Gruppe geteilt werden.
      operationId: getSharedListings
      parameters:
        - $ref: "#/components/parameters/query.PaginationId"
        - $ref: "#/components/parameters/query.FlowIdRequired"
        - $ref: "#/components/parameters/query.UserId"
        - $ref: "#/components/parameters/query.GroupIdList"
      responses:
        "200":
          description: Erfolg
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListingSearchResult"

components:
  schemas:

    Building:
      type: object
      description: Verarbeitete 3D Daten eines Gebäudes
      required:
        - id
        - floors
        - custom_data
      properties:
        id:
          type: string
          description: Id des Gebäudes
        shape_representation:
          $ref: "#/components/schemas/ShapeRepresentation"
        floors:
          type: array
          items:
            $ref: "#/components/schemas/Floor"
        custom_data:
          $ref: "#/components/schemas/FieldData"

    Floor:
      type: object
      required:
        - id
        - level
        - rooms
        - walls
        - slabs
        - custom_data
      properties:
        id:
          type: string
          description: Id des Stockwerks
        shape_representation:
          $ref: "#/components/schemas/ShapeRepresentation"
        level:
          type: integer
          description: Höhe des Stockwerks in Metern
        rooms:
          type: array
          items:
            $ref: "#/components/schemas/Room"
        walls:
          type: array
          items:
            $ref: "#/components/schemas/Wall"
        slabs:
          type: array
          items:
            $ref: "#/components/schemas/ConstructionPart"
        custom_data:
          $ref: "#/components/schemas/FieldData"


    Room:
      type: object
      description: Verarbeitete 3D Daten eines Raumes
      required:
        - id
        - furniture
        - wall_ids
        - slabs
        - custom_data
      properties:
        id:
          type: string
          description: Id des Raumes
        shape_representation:
          $ref: "#/components/schemas/ShapeRepresentation"
        furniture:
          type: array
          items:
            $ref: "#/components/schemas/Furniture"
        wall_ids:
          type: array
          description: Liste der Ids der Wände, die den Raum umschließen
          items:
            type: string
        slabs:
          type: array
          items:
            $ref: "#/components/schemas/ConstructionPart"
        custom_data:
          $ref: "#/components/schemas/FieldData"

    Wall:
      type: object
      required:
        - id
        - room_ids
        - shape_representation
        - openings
        - custom_data
      properties:
        id:
          type: string
          description: Id der Wand
        room_ids:
          type: array
          description: Liste der Ids der Räume, die diese Wand ebenfalls verwenden
          items:
            type: string
        shape_representation:
          $ref: "#/components/schemas/ShapeRepresentation"
        openings:
          type: array
          items:
            $ref: "#/components/schemas/ConstructionPart"
        custom_data:
          $ref: "#/components/schemas/FieldData"

    ConstructionPart:
      type: object
      required:
        - id
        - shape_representation
        - type
        - custom_data
      properties:
        id:
          type: string
          description: Id des Konstruktionsteils
        shape_representation:
          $ref: "#/components/schemas/ShapeRepresentation"
        type:
          type: string
          description: Typ des Konstruktionsteils
        custom_data:
          $ref: "#/components/schemas/FieldData"

    ShapeRepresentation:
      type: object
      required:
        - transformation_matrix
        - shape
      properties:
        transformation_matrix:
          $ref: "#/components/schemas/TransformationMatrix"
        shape:
          $ref: "#/components/schemas/Shape"

    Shape:
      type: object
      properties:
        box:
          $ref: "#/components/schemas/BoundingBox"
        polygon:
          $ref: "#/components/schemas/Polygon"
        ring:
          $ref: "#/components/schemas/Ring"

    Ring:
      type: object
      required:
        - extrusion
        - extrusion_direction
        - radius
        - ring_size
        - start_angle
        - end_angle
        - center
      properties:
        extrusion:
          type: number
          format: double
        extrusion_direction:
          $ref: "#/components/schemas/Vector3D"
        radius:
          type: number
          format: double
        ring_size:
          type: number
          format: double
        start_angle:
          type: number
          format: double
        end_angle:
          type: number
          format: double
        center:
          $ref: "#/components/schemas/Vector2D"

    Polygon:
      type: object
      required:
        - extrusion
        - extrusion_direction
        - vertices
      properties:
        extrusion:
          type: number
          format: double
        extrusion_direction:
          $ref: "#/components/schemas/Vector3D"
        vertices:
          type: array
          items:
            $ref: "#/components/schemas/Vertex"

    Vector2D:
      type: object
      required:
        - x
        - y
      properties:
        x:
          type: number
          format: double
        y:
          type: number
          format: double

    Vector3D:
      type: object
      required:
        - x
        - y
        - z
      properties:
        x:
          type: number
          format: double
        y:
          type: number
          format: double
        z:
          type: number
          format: double

    Vertex:
      type: object
      required:
        - x
        - y
        - z
      properties:
        x:
          type: number
          format: double
        y:
          type: number
          format: double
        z:
          type: number
          format: double

    BoundingBox:
      type: object
      required:
        - width
        - height
        - depth
      properties:
        width:
          type: number
          format: double
          description: Breite
        height:
          type: number
          format: double
          description: Höhe
        depth:
          type: number
          format: double
          description: Tiefe

    TransformationMatrix:
      type: array
      description: Eine 4x4 Matrix, das äußere Array sind die Rows, das Innere Array die Columns. Negativ und positive Values.
      items:
        type: array
        items:
          type: number
          format: double

    Furniture:
      type: object
      required:
        - id
        - shape_representation
        - type
      properties:
        id:
          type: string
          description: Id des Möbelstücks
        shape_representation:
          $ref: "#/components/schemas/ShapeRepresentation"
        type:
          type: string
          description: Typ des Möbelstücks

    BuildingDefaultConstructionInput:
      type: object
      description: All data necessary to create a basic building model
      required:
        - ground_width
        - ground_depth
        - outside_wall_thickness
        - storey_clearance_height
        - storey_count
        - slab_thickness
      properties:
        ground_width:
          type: number
          format: double
          description: Width of the building in meters
        ground_depth:
          type: number
          format: double
          description: Depth of the building in meters
        outside_wall_thickness:
          type: number
          format: double
          description: Thickness of the outside walls in centimeters
        storey_clearance_height:
          type: number
          format: double
          description: Clearance height of a storey in meters
        storey_count:
          type: integer
          description: Number of storeys
        slab_thickness:
          type: number
          format: double
          description: Thickness of the slab between storeys in centimeters

    SharingSettings:
      type: object
      description: |-
        Sharing settings for a listing. Listings can be shared with users or entire groups of users.
      required:
        - shared_with
      properties:
        shared_with:
          type: array
          items:
            $ref: "#/components/schemas/SharedWith"

    SharedWith:
      type: object
      required:
        - id
        - type
        - source
        - share_permission
      properties:
        id:
          type: string
          description: Id of the user or group the listing is shared with
        type:
          type: string
          enum:
            - USER
            - GROUP
        source:
          type: string
          enum:
            - OWNER
            - CONFIG
          description: Who maintained that sharing setting? Config means it was set by the system and cannot be changed by the user.
        share_permission:
          type: string
          enum:
            - READ
            - WRITE
          description: Zugriffsrechte auf das geteilte Listing
          example: "READ"

    CountBillableFeature:
      type: object
      description: Data other to Listing itself to send over to billing service
      required:
        - billable_feature_name
      properties:
        billable_feature_name:
          type: string
          description: Name of the billable feature
        ref:
          type: string
          description: Reference to the billable feature
        dedup_id:
          type: string
          description: |-
            Deduplication id. Use this if you want to count multiple invocations of the same feature for a listing only once. 
            Its uniqueness is checked based on the billable_feature_name and dedup_id combined. 
            For example, if you want to count multiple downloads of the IFC file of a listing only once, 
            you can use the listing id as dedup_id and the billable_feature_name as "IFC_DOWNLOAD".
    VirtualStagingStatus:
      type: object
      required:
        - render_id
        - status
        - progress
      properties:
        render_id:
          type: string
        progress:
          type: number
          format: float
        status:
          type: string
        images:
          type: array
          items:
            $ref: '#/components/schemas/Image'
    ScannedBuildingDataEntry:
      type: object
      required:
        - id
        - timestamp
      properties:
        id:
          type: string
        timestamp:
          type: string
          format: date-time

    ListingCompleteness:
      type: object
      properties:
        field_completeness:
          type: array
          items:
            $ref: "#/components/schemas/FieldCompleteness"

    FieldCompleteness:
      type: object
      required:
        - field_name
        - is_required
        - is_complete
      properties:
        field_name:
          type: string
        is_required:
          type: boolean
        is_complete:
          type: boolean

    FieldsCompletenessInfo:
      type: object
      properties:
        percentage:
          type: number
          format: float
          description: Percentage of fields that are complete, relative to all fields of this category.
          example: 0.9
        field_names:
          type: array
          items:
            type: string


    ListingSyncAvailability:
      type: object
      required:
        - available
        - unavailability_reasons
      properties:
        available:
          type: boolean
          description: If the sync destination is available or not.
        unavailability_reasons:
          type: array
          description: |-
            If the sync destination is not available, then this field contains the reasons why it is not available.
            If the sync destination is available, then this field is empty.
          items:
            type: object
            required:
              - reason
              - hint
            properties:
              reason:
                type: string
                description: The reason why the sync destination is not available.
                example: "MISSING_CONFIG"
              hint:
                type: string
                description: A kind of human readable hint what exactly is missing

    FieldData:
      type: object
      required:
        - data
      properties:
        data:
          type: array
          items:
            type: object
            required:
              - field_name
            properties:
              field_name:
                type: string
              field_value:
                description: string, int, double oder boolean
                example: 56
              array_index:
                type: string
                description: |-
                  Wenn das Feld zu einem Array gehört, dann kann hier der Index angegeben werden.

    FieldDataValidationResult:
      type: object
      properties:
        hints:
          type: array
          items:
            type: object
            required:
              - field_name
              - hint
            properties:
              field_name:
                type: string
                example: living_area
              hint:
                type: string
                example: missing

    FieldSuggestions:
      type: object
      required:
        - suggestions
      properties:
        suggestions:
          type: array
          items:
            type: object
            required:
              - field_name
              - status
            properties:
              status:
                type: string
                description: |-
                  Jeder Vorschlag hat einen Status.

                  - NO_SUGGESTION (Für dieses Feld existiert noch keine Strategie zur Erzeugung eines Vorschlags)
                  - WAITING_FOR_INPUT (Es gibt eine Strategie zur Berechnung, aber es fehlen noch Vorbedingungen. Hinweis auf die fehlenden Vorbedingungen wird im Feld "hint" mitgeliefert)
                  - RUNNING (Es gibt eine Strategie zur Berechnung und die Vorbedingungen sind erfüllt. Der Vorschlag wird gerade berechnet)
                  - SUCCEEDED (Vorschlag wurde berechnet)
                  - FAILED (Fehlschlag)

              approx_finish_time:
                type: string
                format: date-time
                description: |-
                  Zeit, zu der die Suggestion spätestens fertig sein sollte (im Schnitt). Nur gesetzt, wenn der Status RUNNING ist. Eine Grace-Period ist bereits aufgeschlagen.
              field_name:
                type: string
                example: "space_area"

              field_value:
                description: string, int, double oder boolean
                example: 56

              missing_preconditions:
                type: string
                description: |-
                  Textuelle Beschreibung der Vorbedingungen, die noch erfüllt werden müssen, damit der Vorschlag berechnet werden kann. 
                  Dient zu Debug- und Erklärzwecken für Clients. Es ist eine gute Idee, dieses Feld als TRACE oder DEBUG zu loggen.
                  Können z. B. eine Liste von Feldnamen sein oder auch Freitext.
                example: "main_type, sub_type, floorData"

    Listing:
      type: object
      description: Ein Inserat ist ein Exposè einer Immobilie, einer Wohnung, Garage, Pflegeheimwohnung, etc.
      required:
        - id
        - flow_id
        - user_id
        - billing_user_id
        - status
        - visibility
        - fields
        - synchronization
        - creation_date
        - has_floorplan
        - has_3d_model
        - has_building_scan
        - has_virtual_staging
      properties:
        id:
          type: string
          description: |-
            Identifier dieses Inserates. Eigenschaften: 
            - Numerisch. 
            - Immer 9 stellig. 
            - Nicht chronologisch sondern zufällig (damit man nicht ableiten kann wieviele Inserate es gibt oder gegeben hat). 
            - UI anzeigegeeignet.
          example: "153376269"
          maxLength: 9
          minLength: 9
        flow_id:
          type: string
        user_id:
          $ref: "#/components/schemas/UserId"
        billing_user_id:
          $ref: "#/components/schemas/UserId"
        billing_group_id:
          $ref: "#/components/schemas/BillingGroupId"
        creation_date:
          type: string
          format: date-time
          description: Datum und Uhrzeit der Erstellung des Inserates in UTC Zeitzone.
          example: "2020-12-01T12:00:00Z"
        status:
          type: string
          description: Status des Inserates.
          example: "PREVIEW"
          x-extensible-enum:
            - PREVIEW
            - REVIEW
            - APPROVED
            - PUBLISHED
            - DELETED
        visibility:
          type: string
          description: Sichtbarkeit des Inserates.
          example: "PUBLIC"
          enum:
            - PUBLIC
            - PUBLIC_HIDDEN
            - PUBLIC_HIDDEN_PASSWORD_PROTECTED
        fields:
          $ref: "#/components/schemas/FieldData"
        images:
          type: array
          description: Bilder des Inserates.
          items:
            $ref: "#/components/schemas/Image"
        images_zip_download_link:
          type: string
          description: |-
            Link zum Download eines ZIP Archivs mit allen Bildern des Inserates.
            Der Link muss erst über die entsprechende API Funktion generiert werden.
        building:
          $ref: "#/components/schemas/Building"
        synchronization:
          type: object
          required:
            - enabled_flags
          properties:
            enabled_flags:
              type: array
              description: |-
                - Liste der 3rd party Sync Destinations, die aktiviert sind.
                - Wenn die Liste leer ist, dann ist keine Synchronisierung aktiviert.
              items:
                $ref: "#/components/schemas/ListingSyncEnabledFlag"

        pagination_id:
          type: string
          description: Hex String Id to be used for pagination. Only for internal use and only present if clients' intent is to perform a paginated search.
          example: "63751998c7fc71382a834f20"
        demoListing:
          type: boolean
          description: If true, then this listing is a demo listing.
          example: false
        latestValuations:
          $ref: "#/components/schemas/Valuations"
        has_floorplan:
          type: boolean
          description: If true, then this listing has a floorplan, either by scan or by uploaded image.
        has_3d_model:
          type: boolean
          description: If true, then this listing has a 3d model.
        has_building_scan:
          type: boolean
          description: If true, then this listing has a building scan.
        has_virtual_staging:
          type: boolean
          description: If true, then this listing has at least one image virtually staged.

    ListingSyncEnabledFlag:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          description: See ListingSyncDestination.kt
        synced_listing_url:
          type: string
          description: |-
            - Die URL, die das Listing bei der 3rd party hat.

    ListingStatistics:
      type: object
      description: Statistische Informationen zu einem Inserat, z. B. Anzahl der Aufrufe.
      required:
        - view_count
      properties:
        view_count:
          type: integer
          format: int32
          minimum: 0
          description: Anzahl der Aufrufe des Inserates.
          example: 10

    ListingSearchResult:
      type: object
      description: |-
        - Ergebnis einer Suche nach Inseraten.
        - Enthält eine Liste von Inserat-Ids und Metadaten zur Pagination.
      required:
        - count
        - items
      properties:
        count:
          type: integer
          description: Anzahl der Inserate insgesamt, die auf die Query matchen.
          example: 10
        next_page_id:
          type: string
          description: |-
            Id, die für die nächste Seite der Suche verwendet werden kann.
          example: "63751998c7fc71382a834f20"
        items:
          type: array
          items:
            type: string

    BuildingMasses:
      type: object
      properties:
        building:
          $ref: "#/components/schemas/Building"

    EnrichedListing:
      allOf:
        - $ref: "#/components/schemas/Listing"
        - type: object
          description: Enrichment of the Listing.
          properties:
            geo_localization:
              type: object
              description: Geolokalisierte Informationen.
              required:
                - poi_groups
                - attractiveness
                - admin_boundary_ids
              properties:
                poi_groups:
                  type: array
                  items:
                    $ref: "#/components/schemas/PoiGroup"
                attractiveness:
                  $ref: "#/components/schemas/GeoAttractiveness"
                admin_boundary_ids:
                  type: object
                  description: |-
                    Die IDs der administrativen Grenzen, in denen sich das Inserat befindet. 
                    Die IDs sind in der Reihenfolge von der feinsten bis zur groben Granularität sortiert.
                  properties:
                    district_id:
                      type: string
                    city_id:
                      type: string
                    county_id:
                      type: string
                    state_id:
                      type: string

    GeoAttractiveness:
      type: object
      description: |-
        - Attraktivität des Inserats. 
        - Attraktivität ist ein Wert zwischen 0 bis X, wobei X am attraktivsten ist.
        - Der maximal erreichbare Wert (achievable_score) kann sich ändern, daher sollte der Wert als Grundwert für eine Prozentrechnung genutzt werden.
        - Ist der "achievable_score" bspw. 80 und der "score" ist 60, so beträgt die Attraktivität also 60/80 = 75% von 100%.
      required:
        - achievable_score
        - score
        - categories
      properties:
        achievable_score:
          type: integer
          description: Maximal erreichbarer Wert.
          example: 80
        score:
          type: integer
          example: 60
        categories:
          type: array
          items:
            $ref: "#/components/schemas/AttractivenessCategory"

    AttractivenessCategory:
      type: object
      required:
        - category_key
        - is_data_missing
        - achievable_score
        - score
        - rating_criteria
      properties:
        category_key:
          type: string
          description: Identifier der Kategorie
          example: "microlocation"
        achievable_score:
          type: integer
          description: Die Anzahl der überhaupt erreichbaren Punkte.
          example: 55
        score:
          type: integer
          description: Summe aller Punkte (Gesamtbewertung)
          example: 50
        rating_criteria:
          type: array
          items:
            $ref: "#/components/schemas/RatingCriteria"

    RatingCriteria:
      type: object
      description: Konkretes Bewertungsmerkmal.
      required:
        - key
        - achievable_score
        - score
        - is_data_missing
      properties:
        key:
          type: string
          description: Identifier des Merkmals.
          example: "leisure_and_activities"
        achievable_score:
          type: integer
          description: Die Anzahl der überhaupt erreichbaren Punkte.
          example: 5
        score:
          type: integer
          description: Summe aller Punkte (Gesamtbewertung)
          example: 5
        is_data_missing:
          type: boolean
          description: |-
            Wenn true, dann sind keine Daten für das Merkmal vorhanden. In diesem Fall ist die Bewertung nicht möglich und die Punktzahl ist 0. In diesem Fall
            sollte der Score anders dargestellt werden, z. B. "grau" für den Bereich, der unbewertet bleibt.
            Wenn false, dann sind Daten vorhanden und die Bewertung ist möglich.
        points_of_interest:
          type: array
          description: |-
            Die Liste enthält (nur) für die Lage spezifischen Bewertungskriterien eine ausführliche Liste der in Betracht gezogenen POIs.
          items:
            $ref: "#/components/schemas/AttractivenessPoi"

    AttractivenessPoi:
      type: object
      required:
        - poi_type
        - available
        - search_radius_m
      properties:
        poi_type:
          type: string
        available:
          type: boolean
          description: If the POI is available or not
        search_radius_m:
          type: integer
          description: Maximaler Suchradius für den POI Type.
        distance_m:
          type: integer
          description: When the POI is available, this field shows the distance in meters.

    PoiType:
      type: string
      description: Typ des POIs. Siehe PoiType.kt in ListingService - wird 1:1 durchgereicht als String.
      example: SUPERMARKET

    PoiGroup:
      type: object
      description: Gruppe von zusammengehörigen POIs.
      required:
        - search_radius_m
        - poi_type
        - poi_list
        - parent_groups
      properties:
        search_radius_m:
          type: integer
          description: Suchradius dieser Gruppe
          example: 4000
        poi_type:
          $ref: "#/components/schemas/PoiType"
        poi_list:
          type: array
          description: Die konkreten POIs.
          items:
            $ref: "#/components/schemas/POI"
        parent_groups:
          type: array
          description: |-
            - Die Elterngruppen dieser Gruppe. Dient der Gruppierbarkeit der POIs.
            - Z. B. könnte man eine Gruppe "Freizeitaktivitäten" haben, die die Gruppen "Kino", "Theater", "Freizeitpark" und "Zoo" enthält.
            - Ein POI Typ kann in mehreren Gruppen vorkommen. Z. B. Drogerie in Gruppe "Dinge des täglichen Bedarfs" und "Shopping".
          items:
            type: string
            description: Für Liste von Enums siehe PoiParentGroupType.kt in ListingService.
    POI:
      type: object
      required:
        - distance_m
        - latitude
        - longitude
      description: Point of Interest mit Abstand zum Inserat.
      properties:
        id:
          type: string
          description: Id des POI, abgeleitet aus dem Data Objekt.
          example: "12345Id"
        display_name:
          type: string
          description: Anzeigename des POI zum Beispiel als Marker
          example: "Edeka Hansen"
        distance_m:
          type: integer
          description: Distanz zum Inserat in Meter
          example: 1400
        latitude:
          type: number
          format: double
          description: Breitengrad.
        longitude:
          type: number
          format: double
          description: Längengrad.
        details:
          type: array
          description: |-
            - Details zum POI.
            - Z. B. Öffnungszeiten, Telefonnummer, Webseite, etc.
          items:
            type: object
            required:
              - key
              - value
            properties:
              key:
                type: string
                description: Schlüssel des Details.
                example: "phone"
              value:
                type: string
                description: Wert des Details.
                example: "+49 123 456789"

    FloorModel3D:
      type: object
      description: Ein Raumscan als usdz Datei.
      required:
        - id
        - path
        - floor
        - caption
      properties:
        id:
          type: string
          description: Eindeutige Id des Bildes.
        floor:
          type: integer
          description: Etage zu dem der Raumplan gehört.
        caption:
          type: string
          description: Beschreibung des Raumplans
          example: "Raumplan des 1. Stockwerks"
        path:
          type: string
          description: Vollständiger, relativer Pfad um den Roomplan zu beziehen.
    Image:
      type: object
      description: Ein Bild eines Inserates.
      required:
        - id
        - type
        - width
        - height
        - path_original
        - path_thumb
        - path_mini
      properties:
        id:
          type: string
          description: Eindeutige Id des Bildes.
        type:
          type: string
          description: Art des Fotos. Z. B. UserUpload (Photo), Grundriss, etc.
          x-extensible-enum:
            - USER_UPLOAD
            - ROOMPLAN_2D
            - ROOMPLAN_3D
            - VIRTUAL_STAGING
        imageInfo:
          type: object
          description: Metadaten des Bildes.
          properties:
            caption:
              type: string
              description: Beschreibung des Bildes.
              example: "Wohnzimmer"
        path_original:
          type: string
          description: Vollständiger, relativer Pfad um das Bild in größter Auflösung abzurufen.
        path_thumb:
          type: string
          description: Vollständiger, relativer Pfad um das Bild in Thumbnail Auflösung abzurufen.
        path_mini:
          type: string
          description: Vollständiger, relativer Pfad um das Bild in Mini Auflösung abzurufen.
        width:
          type: integer
          description: Breite des Bildes in Pixel.
        height:
          type: integer
          description: Höhe des Bildes in Pixel.
        virtual_staging:
          type: object
          required:
            - original_image_id
            - room_type
            - furniture_style
          properties:
            original_image_id:
              type: string
            room_type:
              type: string
            furniture_style:
              type: string

    ListingPublished:
      type: object
      description: Event Payload, der gesendet wird wenn das Event vom Typ ListingPublished ist.
      required:
        - listing_id
      properties:
        listing_id:
          type: string
          description: Inserat Id, das vom User gepublished wurde und nun in den Freigabeprozess gehen soll.
          example: "123456789"

    UserId:
      type: string
      description: UserId associated with the listing.
      example: "Alphanumeric123"

    UserBillingId:
      type: string
      description: BillingId associated with the listing.
      example: "Alphanumeric123"

    BillingGroupId:
      type: string
      description: BillingGroupId associated with the listing.
      example: "Alphanumeric123"

    ScannedBuildingGet:
      type: object
      description: ScannedBuilding enriched with calculated data.
      required:
        - id
        - creation_date
        - version
        - rooms
        - surfaces
        - objects
        - sections
        - is_complete
        - floor_count
        - was_raw_data_valid
        - calculated_data
      properties:
        id:
          type: string
        creation_date:
          type: string
          format: date-time
          description: Datum und Uhrzeit der Erstellung des Gebäude-Modells in UTC Zeitzone.
          example: "2020-12-01T12:00:00Z"
        version:
          type: string
        rooms:
          type: array
          items:
            $ref: "#/components/schemas/ARRoomGet"
        surfaces:
          type: array
          items:
            $ref: "#/components/schemas/ARSurfaceGet"
        objects:
          type: array
          items:
            $ref: "#/components/schemas/ARObject"
        sections:
          type: array
          items:
            $ref: "#/components/schemas/ARSection"
        is_complete:
          type: boolean
          description: |-
            Wenn true, dann sind alle Räume, Surfaces und Objects vollständig gescannt.
            Wenn false, dann sind noch nicht alle Räume, Surfaces und Objects vollständig gescannt und dies ist nur ein Zwischenstand.
        floor_count:
          type: integer
          description: |-
            Anzahl der Stockwerke.
        was_raw_data_valid:
          type: boolean

    ARRoomGet:
      type: object
      required:
        - id
        - version
        - floor_level
        - surfaces
        - objects
        - sections
        - was_raw_data_valid
        - custom_data
      properties:
        id:
          type: string
        version:
          type: string
        surfaces:
          type: array
          items:
            $ref: "#/components/schemas/ARSurfaceGet"
        objects:
          type: array
          items:
            $ref: "#/components/schemas/ARObject"
        sections:
          type: array
          items:
            $ref: "#/components/schemas/ARSection"
        floor_level:
          type: integer
        was_raw_data_valid:
          type: boolean
        custom_data:
          $ref: "#/components/schemas/FieldData"


    ARSurfaceGet:
      type: object
      required:
        - id
        - category
        - confidence
        - completed_edges
        - bounding_box
        - measurements
        - openings
        - transformation
        - polygon
        - floor_level
        - was_raw_boundingbox_valid
        - was_raw_transformation_valid
        - was_raw_polygon_valid
      properties:
        id:
          type: string
        parent_id:
          type: string
        category:
          $ref: "#/components/schemas/ARSurfaceCategory"
        confidence:
          $ref: "#/components/schemas/ARConfidence"
        completed_edges:
          type: array
          items:
            $ref: "#/components/schemas/ARSurfaceEdge"
        bounding_box:
          $ref: "#/components/schemas/ARBoundingBox3D"
        openings:
          type: array
          items:
            $ref: "#/components/schemas/ARSurfaceGet"
        transformation:
          $ref: "#/components/schemas/ARMatrix4D"
        curve:
          $ref: "#/components/schemas/ARSurfaceCurve"
        polygon:
          $ref: "#/components/schemas/ARVector3DArray"
        floor_level:
          type: integer
        was_raw_boundingbox_valid:
          type: boolean
        was_raw_transformation_valid:
          type: boolean
        was_raw_polygon_valid:
          type: boolean

    ScannedBuilding:
      type: object
      description: Building Objekt (3D Scandaten)
      required:
        - id
        - creation_date
        - version
        - rooms
        - surfaces
        - objects
        - sections
        - is_complete
        - floor_count
        - was_raw_data_valid
      properties:
        id:
          type: string
        creation_date:
          type: string
          format: date-time
          description: Datum und Uhrzeit der Erstellung des Gebäude-Modells in UTC Zeitzone.
          example: "2020-12-01T12:00:00Z"
        version:
          type: string
        rooms:
          type: array
          items:
            $ref: "#/components/schemas/ARRoom"
        surfaces:
          type: array
          items:
            $ref: "#/components/schemas/ARSurface"
        objects:
          type: array
          items:
            $ref: "#/components/schemas/ARObject"
        sections:
          type: array
          items:
            $ref: "#/components/schemas/ARSection"
        is_complete:
          type: boolean
          description: |-
            Wenn true, dann sind alle Räume, Surfaces und Objects vollständig gescannt.
            Wenn false, dann sind noch nicht alle Räume, Surfaces und Objects vollständig gescannt und dies ist nur ein Zwischenstand.
        floor_count:
          type: integer
          description: |-
            Anzahl der Stockwerke.
        was_raw_data_valid:
          type: boolean

    ARRoom:
      type: object
      required:
        - id
        - version
        - floor_level
        - surfaces
        - objects
        - sections
        - was_raw_data_valid
      properties:
        id:
          type: string
        version:
          type: string
        surfaces:
          type: array
          items:
            $ref: "#/components/schemas/ARSurface"
        objects:
          type: array
          items:
            $ref: "#/components/schemas/ARObject"
        sections:
          type: array
          items:
            $ref: "#/components/schemas/ARSection"
        floor_level:
          type: integer
        was_raw_data_valid:
          type: boolean
        custom_data:
          $ref: "#/components/schemas/FieldData"

    ARSection:
      type: object
      required:
        - center
        - label
        - floor_level
        - was_raw_center_valid
      properties:
        center:
          $ref: "#/components/schemas/ARVector3D"
        label:
          type: string
        floor_level:
          type: integer
        was_raw_center_valid:
          type: boolean

    ARSurface:
      type: object
      required:
        - id
        - category
        - confidence
        - completed_edges
        - bounding_box
        - transformation
        - polygon
        - floor_level
        - was_raw_boundingbox_valid
        - was_raw_transformation_valid
        - was_raw_polygon_valid
      properties:
        id:
          type: string
        parent_id:
          type: string
        category:
          $ref: "#/components/schemas/ARSurfaceCategory"
        confidence:
          $ref: "#/components/schemas/ARConfidence"
        completed_edges:
          type: array
          items:
            $ref: "#/components/schemas/ARSurfaceEdge"
        bounding_box:
          $ref: "#/components/schemas/ARBoundingBox3D"
        transformation:
          $ref: "#/components/schemas/ARMatrix4D"
        curve:
          $ref: "#/components/schemas/ARSurfaceCurve"
        polygon:
          $ref: "#/components/schemas/ARVector3DArray"
        floor_level:
          type: integer
        was_raw_boundingbox_valid:
          type: boolean
        was_raw_transformation_valid:
          type: boolean
        was_raw_polygon_valid:
          type: boolean

    ARSurfaceCurve:
      type: object
      required:
        - radius
        - start_angle
        - end_angle
        - center
        - was_raw_radius_valid
        - was_raw_start_angle_valid
        - was_raw_end_angle_valid
        - was_raw_center_valid
      properties:
        radius:
          type: number
          format: double
        start_angle:
          type: number
          format: double
        end_angle:
          type: number
          format: double
        center:
          $ref: "#/components/schemas/ARVector2D"
        was_raw_radius_valid:
          type: boolean
        was_raw_start_angle_valid:
          type: boolean
        was_raw_end_angle_valid:
          type: boolean
        was_raw_center_valid:
          type: boolean

    ARObject:
      type: object
      required:
        - id
        - category
        - confidence
        - bounding_box
        - transformation
        - attributes
        - floor_level
        - was_raw_boundingbox_valid
        - was_raw_transformation_valid
      properties:
        id:
          type: string
        parent_id:
          type: string
        category:
          $ref: "#/components/schemas/ARObjectCategory"
        confidence:
          $ref: "#/components/schemas/ARConfidence"
        bounding_box:
          $ref: "#/components/schemas/ARBoundingBox3D"
        transformation:
          $ref: "#/components/schemas/ARMatrix4D"
        attributes:
          type: array
          description: A collection of details that describe a particular object in the room.
          items:
            type: string
        floor_level:
          type: integer
        was_raw_boundingbox_valid:
          type: boolean
        was_raw_transformation_valid:
          type: boolean

    ARMatrix4D:
      type: array
      description: Eine 4x4 Matrix, das äußere Array sind die Rows, das Innere Array die Columns. Negativ und positive Values.
      items:
        type: array
        items:
          type: number
          format: double

    ARVector3DArray:
      type: array
      description: Alle Values müssen immer positiv sein und immer 3 Values
      items:
        $ref: "#/components/schemas/ARVector3D"

    ARVector2D:
      type: array
      description: Hat immer genau 2 Values.
      items:
        type: number
        format: double
      example: [ 11.1, -22.12 ]

    ARVector3D:
      type: array
      description: Hat immer genau 3 Values.
      items:
        type: number
        format: double
      example: [ -11.1, 22.12, -32.13 ]

    ARBoundingBox3D:
      type: array
      description: Alle Values müssen immer positiv sein und immer 3 Values.
      items:
        type: number
        format: double
      example: [ 11.1, 22.12, 32.13 ]

    ARConfidence:
      type: string
      x-extensible-enum:
        - LOW
        - MEDIUM
        - HIGH

    ARSurfaceEdge:
      type: string
      x-extensible-enum:
        - TOP
        - RIGHT
        - BOTTOM
        - LEFT

    ARSurfaceCategory:
      type: string
      x-extensible-enum:
        - WALL
        - OPENING
        - WINDOW
        - OPENED_DOOR
        - CLOSED_DOOR

    ARObjectCategory:
      type: string
      x-extensible-enum:
        - STORAGE
        - REFRIGERATOR
        - STOVE
        - BED
        - SINK
        - WASHER_DRYER
        - TOILET
        - BATHTUB
        - OVEN
        - DISHWASHER
        - TABLE
        - SOFA
        - CHAIR
        - FIREPLACE
        - TELEVISION
        - STAIRS

    PropertyValuationRequest:
      type: object
      description: Request to get a property valuation
      required:
        - listing_ids
      properties:
        listing_ids:
          type: array
          description: List of listing ids to get the valuation for
          items:
            type: string
            example: "123456789"

    PropertyValuationResponse:
      type: object
      description: Response to get a property valuation
      required:
        - valuations
      properties:
        valuations:
          type: array
          description: List of valuations for the requested listings
          items:
            $ref: "#/components/schemas/PropertyValuation"

    PropertyValuation:
      type: object
      description: Valuation for a single listing
      required:
        - listing_id
        - valuations
      properties:
        listing_id:
          type: string
          description: Id of the listing
          example: "123456789"
        valuations:
          type: array
          description: List of valuations with different dates
          items:
            $ref: "#/components/schemas/Valuation"

    Valuations:
      type: object
      description: Valuations for a single listing
      required:
        - valuations
      properties:
        valuations:
          type: array
          description: List of valuations with different dates
          items:
            $ref: "#/components/schemas/Valuation"

    Valuation:
      type: object
      required:
        - valuationDate
      properties:
        valuationDate:
          type: string
          format: date
          description: date of the valuation
          example: "2020-12-01"
        currency:
          type: string
          enum:
            - CHF
            - CZK
            - EUR
            - GBP
            - JPY
        coordinates:
          $ref: "#/components/schemas/Coordinates"
        salePrice:
          type: integer
          description: Only present if dealType is sale
        salePriceRange:
          $ref: "#/components/schemas/ValueRange"
          description: Only present if dealType is sale
        rentGross:
          type: integer
          description: Only present if dealType is rent
        rentGrossRange:
          $ref: "#/components/schemas/ValueRange"
          description: Only present if dealType is rent
        rentNet:
          type: integer
          description: Only present if dealType is rent
        rentNetRange:
          $ref: "#/components/schemas/ValueRange"
          description: Only present if dealType is rent
        confidence:
          type: string
          enum:
            - poor
            - medium
            - good

    Coordinates:
      type: object
      required:
        - latitude
        - longitude
      properties:
        latitude:
          type: number
          format: double
        longitude:
          type: number
          format: double

    VirtualStagingRequest:
      type: object
      description: Virtual Staging Request
      required:
        - room_type
        - style
      properties:
        room_type:
          type: string
        style:
          type: string

    ValueRange:
      type: object
      required:
        - lower
        - upper
      properties:
        lower:
          type: number
          format: double
        upper:
          type: number
          format: double

  parameters:
    query.FloorIdOptional:
      name: floor-id
      in: query
      description: Id des Floors. Setzen um ein bestehenden Floor zu überschreiben.
      required: false
      schema:
        type: string
    query.ListingIdOptional:
      in: query
      description: Id des Inserates. Operation wird als Update ausgeführt wenn angegeben. Weglassen für Inseratanlage.
      required: false
      allowEmptyValue: false
      style: form
      explode: false
      name: listing_id
      schema:
        type: string
    path.ListingIdRequired:
      name: listing-id
      in: path
      description: Id des Inserates.
      required: true
      style: simple
      schema:
        type: string
    path.DataIdRequired:
      name: data-id
      in: path
      description: Id des FloorData Objektes.
      required: true
      style: simple
      schema:
        type: string
    path.ImageIdRequired:
      name: image-id
      in: path
      description: Id des Images.
      required: true
      style: simple
      schema:
        type: string
    path.EventType:
      name: event-type
      in: path
      description: Typ des DomainEvents.
      required: true
      style: simple
      schema:
        type: string
        enum:
          - GeoLocalizationIndexBuilt
          - ListingPublished
    path.BuildingDataIdRequired:
      name: building-data-id
      in: path
      description: Id des ScannedBuilding Objektes.
      required: true
      style: simple
      schema:
        type: string
    query.ConstructionPartIdRequired:
      name: construction-part-id
      in: query
      description: Id des ConstructionPart Objektes.
      required: true
      schema:
        type: string
    path.ConstructionPartIdRequired:
      name: construction-part-id
      in: path
      description: Id des ConstructionPart Objektes.
      required: true
      style: simple
      schema:
        type: string
    query.ListingIdShortRequired:
      name: id
      in: query
      description: ListingId im Path in Kurzschreibweise.
      allowEmptyValue: false
      required: true
      style: form
      explode: false
      schema:
        type: string
    query.FloorIdRequired:
      name: floor-id
      in: query
      description: Id des Floors.
      required: true
      schema:
        type: string
    query.FloorHeightOffset:
      name: floor-height-offset
      in: query
      description: Offset der Stockwerks ShapeRepresentation (Höhenwert der Translation).
      required: true
      schema:
        type: number
        format: double
    query.ActionRequired:
      name: action
      in: query
      description: Auszuführende Aktion, z. B. approve, reject, review.
      allowEmptyValue: false
      required: true
      style: form
      explode: false
      schema:
        type: string
    query.RenderIdOptional:
      name: render-id
      in: query
      description: Id des Renders.
      required: false
      style: form
      schema:
        type: string
    query.ReasonOptional:
      name: reason
      in: query
      description: Auszuführende Aktion, z. B. approve, reject, review.
      allowEmptyValue: false
      required: false
      style: form
      explode: false
      schema:
        type: string
    query.ListingIds:
      in: query
      description: Liste der gewuenschten Inserat IDs
      required: false
      allowEmptyValue: false
      style: form
      explode: false
      name: listing_ids
      schema:
        type: array
        items:
          type: string
    query.UserId:
      in: query
      description: UserId, für die die Inserate gesucht werden sollen.
      required: false
      allowEmptyValue: false
      style: form
      explode: false
      name: user_id
      schema:
        type: string
        example: "Alphanumeric123"
    query.UserIdRequired:
      in: query
      description: UserId, für die die Inserate gesucht werden sollen.
      required: true
      allowEmptyValue: false
      style: form
      explode: false
      name: user_id
      schema:
        type: string
        example: "Alphanumeric123"
    query.GroupIdOptional:
      in: query
      description: GroupId, für die die Inserate gesucht werden sollen.
      required: false
      allowEmptyValue: false
      style: form
      explode: false
      name: group_id
      schema:
        type: string
        example: "Alphanumeric123"
    query.OtherListingIdRequired:
      in: query
      description: Id des anderen Inserates.
      required: true
      allowEmptyValue: false
      style: form
      explode: false
      name: other_listing_id
      schema:
        type: string
        example: "Alphanumeric123"
    query.CreateNewListing:
      in: query
      description: Ob ein neues Inserat erstellt werden soll.
      required: false
      allowEmptyValue: false
      style: form
      explode: false
      name: create_new_listing
      schema:
        type: boolean
    query.StoreyNumberToBeginWith:
      in: query
      description: Offset der Stockwerks ShapeRepresentation (Höhenwert der Translation).
      required: true
      allowEmptyValue: false
      style: form
      explode: false
      name: storey_number_to_begin_with
      schema:
        type: number
        format: double
    query.FlowId:
      in: query
      description: FlowId, für die die Inserate gesucht werden sollen.
      required: false
      allowEmptyValue: false
      style: form
      explode: false
      name: flow_id
      schema:
        type: string
        example: "Alphanumeric123"
    query.FlowIdRequired:
      in: query
      description: FlowId, für die die Inserate gesucht werden sollen.
      required: true
      allowEmptyValue: false
      style: form
      explode: false
      name: flow_id
      schema:
        type: string
        example: "Alphanumeric123"
    query.GroupIdList:
      in: query
      description: Liste von GroupIds, für die die Inserate gesucht werden sollen.
      required: false
      allowEmptyValue: false
      style: form
      explode: false
      name: group_ids
      schema:
        type: array
        items:
          type: string
          example: "Alphanumeric123"
    query.PaginationId:
      name: pagination-id
      in: query
      required: false
      description: NICHT die ListingId! Gibt die pagination_id des letzten Items der vorigen Page an. Diese ist im Listing Response enthalten. Für die erste Page null lassen.
      schema:
        type: string
        example: "63751998c7fc71382a834f20"
    query.Limit:
      name: limit
      in: query
      required: false
      description: Specifies the amount of items to be returned by each page
      schema:
        type: integer
        format: int32
        minimum: 0
    query.ListingVisibilityRequired:
      name: visibility
      in: query
      required: true
      description: Sichtbarkeit des Inserats.
      schema:
        type: string
        default: PUBLIC
        enum:
          - PUBLIC
          - PUBLIC_HIDDEN
          - PUBLIC_HIDDEN_PASSWORD_PROTECTED
    query.ListingPasswordOptional:
      name: visibility-password
      in: query
      required: false
      description: Passwort für die Sichtbarkeit des Inserats. Nur setzen, wenn visibility PUBLIC_HIDDEN_PASSWORD_PROTECTED ist.
      schema:
        type: string
    query.EnableDecluttering:
      name: enable_decluttering
      in: query
      required: true
      schema:
        type: boolean
    query.SyncDestination:
      name: sync-destination
      in: query
      required: true
      description: |-
        - Die Destination, die synchronisiert werden soll.
        - Siehe ListingSyncDestination.kt in ListingService.
      schema:
        type: string
    header.UserIdRequired:
      in: header
      description: UserId passed in the header.
      required: true
      name: X-User-ID
      schema:
        type: string
        example: "423a1c9b-e273-4209-9824-ad44932b8b8d"
    header.Testing:
      in: header
      description: Whether this listing is for testing purposes.
      required: false
      name: X-Testing
      schema:
        type: boolean
        example: false
    header.CreateDemo:
      in: header
      description: Whether this listing is for demo purposes.
      required: false
      name: X-Create-Demo
      schema:
        type: boolean
        example: false
