const umlautMap: { [key: string]: string } = {
  ä: 'a',
  ö: 'o',
  ü: 'u',
  Ä: 'A',
  Ö: 'O',
  Ü: 'U',
  ß: 'ss',
  á: 'a',
  é: 'e',
  í: 'i',
  ó: 'o',
  ú: 'u',
  Á: 'A',
  É: 'E',
  Í: 'I',
  Ó: 'O',
  Ú: 'U',
  ñ: 'n',
  Ñ: 'N'
};

export function normalizeStringGermanAndSpanish(input: string): string {
  let normalized = input.replace(/[äöüÄÖÜßáéíóúÁÉÍÓÚñÑ]/g, (match) => umlautMap[match]);
  normalized = normalized.replace(/[-_=+.,!@#$%^&*(){}[\]<>?/|\\`~"';:]/g, ' ');
  normalized = normalized.replace(/\s+/g, ' ').trim().toLowerCase();
  return normalized;
}

export function searchFilter(query: string | null, fieldValues: (string | undefined)[]): boolean {
  const searchableString = fieldValues.filter((i) => !!i).join(' ');
  const queryNormalized = normalizeStringGermanAndSpanish(query || '');
  const queryTokens = queryNormalized.split(/\s+/).map((token) => token.trim());
  return queryTokens.every((token) => searchableString.includes(token));
}
