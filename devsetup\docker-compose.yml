version: '3.9'

services:

  dbt-projectconfigurator-mongo:
    container_name: dbt-projectconfigurator-mongo
    image: mongo:7.0.9-jammy
    ports:
      - "27027:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: mongo
      MONGO_INITDB_DATABASE: admin
    volumes:
      - ${MONGO_PROJECTCONFIGURATOR_DATA_DIRECTORY}:/data/db:rw
      - ./mongo/mongo-projectconfigurator-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    restart: unless-stopped

networks:
  default:
    name: doorbit
volumes:
  mongodb_master_data:
    driver: local
  data:
    external: true
