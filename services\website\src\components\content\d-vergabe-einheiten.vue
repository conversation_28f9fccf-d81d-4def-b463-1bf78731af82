<template>
  <v-snackbar v-if="leistungSelectionChangeError" v-model="showLeistungSelectionChangeError" vertical>
    <v-card-subtitle class="text-pre-wrap"
      >{{ leistungSelectionChangeError.name }}: {{ leistungSelectionChangeError.message }}</v-card-subtitle
    >
    <v-card-text>{{ leistungSelectionChangeError.graphQLErrors }}</v-card-text>
    <template #actions>
      <v-btn color="indigo" variant="text" @click="showLeistungSelectionChangeError = false">
        {{ t('common.ok') }}
      </v-btn>
    </template>
  </v-snackbar>
  <v-dialog
    :model-value="!!leistungCreationContext"
    :max-width="$vuetify.display.smAndDown ? undefined : 500"
    :fullscreen="$vuetify.display.smAndDown"
    @update:model-value="() => (leistungCreationContext = null)"
  >
    <d-leistung-creation-form
      v-if="leistungCreationContext"
      :creation-context="leistungCreationContext"
      @finish="() => (leistungCreationContext = null)"
    />
  </v-dialog>
  <v-dialog
    :model-value="!!activeLeistungId"
    max-width="1400"
    :fullscreen="$vuetify.display.smAndDown"
    @update:model-value="() => (activeLeistungId = null)"
  >
    <d-leistung v-if="!!activeLeistungId" v-model="activeLeistungId" :is-fullscreen="$vuetify.display.smAndDown" />
  </v-dialog>
  <div class="bg-grey-lighten-4 pt-6 h-100">
    <div class="mx-4 d-flex">
      <v-text-field
        v-model="search"
        hide-details
        density="compact"
        variant="outlined"
        bg-color="white"
        rounded
        :label="t('domain.vergabeEinheitenFilters.searchLabel')"
        :prepend-inner-icon="mdiMagnify"
        autofocus
        clearable
        tabindex="0"
      />
      <v-select
        v-model="filter"
        hide-details
        variant="outlined"
        density="compact"
        bg-color="white"
        rounded
        clearable
        class="d-vergabe-einheiten__filter mx-2"
        :label="t('domain.vergabeEinheitenFilters.filterLabel')"
        :items="[
          { title: t('domain.vergabeEinheitenFilters.filterItems.selected'), value: FILTER_IS_SELECTED },
          { title: t('domain.vergabeEinheitenFilters.filterItems.invalid'), value: FILTER_HAS_WARNINGS },
          { title: t('domain.vergabeEinheitenFilters.filterItems.notConfigured'), value: FILTER_NOT_CONFIGURED },
          {
            title: t('domain.vergabeEinheitenFilters.filterItems.partiallyConfigured'),
            value: FILTER_PARTIALLY_CONFIGURED
          },
          { title: t('domain.vergabeEinheitenFilters.filterItems.configured'), value: FILTER_COMPLETELY_CONFIGURED }
        ]"
        tabindex="0"
      />
      <v-spacer />
      <v-btn color="black" variant="text" :icon="mdiCollapseAll" @click="collapse = new Date()" />
    </div>
    <div class="ml-2 mt-2">
      <v-chip
        v-for="moduleConfig of moduleConfigs"
        :key="moduleConfig.name"
        size="large"
        rounded
        class="ma-2"
        :style="moduleConfig.isActive ? '' : 'opacity: 0.5'"
        variant="flat"
        :color="moduleConfig.color"
        @click="toggleModuleActive(moduleConfig.name)"
      >
        <span class="font-weight-bold mr-2">{{ moduleConfig.name }}</span>
        <span>({{ currencyFormat(moduleConfig.offerPriceGross, locale) }})</span>
      </v-chip>
    </div>
    <v-card class="ma-4">
      <d-vergabe-einheiten-table
        v-if="configurationTable?.vergabeEinheiten.length"
        :search="searchAndFilter"
        @create-leistung="leistungCreationContext = $event"
        @open-leistung="activeLeistungId = $event"
      />
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { mdiCollapseAll, mdiMagnify } from '@mdi/js';
import { computed, ref, watch } from 'vue';
import DLeistung from '@/components/content/leistung/d-leistung.vue';
import DLeistungCreationForm from '@/components/content/d-leistung-creation-form.vue';
import { LeistungCreationContext } from '@/components/content/LeistungCreationContext';
import DVergabeEinheitenTable from '@/components/content/vergabe-einheiten-table/d-vergabe-einheiten-table.vue';
import {
  FILTER_COMPLETELY_CONFIGURED,
  FILTER_HAS_WARNINGS,
  FILTER_IS_SELECTED,
  FILTER_NOT_CONFIGURED,
  FILTER_PARTIALLY_CONFIGURED
} from '@/components/content/vergabe-einheiten-table/filterToken';
import { useConfigurationTableService } from '@/service/use-configuration-table-service';
import { currencyFormat } from '@/utility/filter';

const { t, locale } = useI18n();
const search = ref('');
const showLeistungSelectionChangeError = ref(false);
const filter = ref<string | null>(null);
const leistungCreationContext = ref<LeistungCreationContext | null>(null);
const activeLeistungId = ref<string | null>(null);
const { configurationTable, leistungSelectionChangeError, moduleConfigs, collapse } = useConfigurationTableService();
const searchAndFilter = computed(() => [search.value, filter.value].join(' '));

watch(
  leistungSelectionChangeError,
  (err) => {
    if (err) {
      showLeistungSelectionChangeError.value = true;
    }
  },
  { immediate: true }
);

function toggleModuleActive(moduleName: string) {
  const existingIndex = moduleConfigs.value.findIndex((config) => config.name === moduleName);
  const existingEl = moduleConfigs.value[existingIndex];
  moduleConfigs.value = [
    ...moduleConfigs.value.slice(0, existingIndex),
    { ...existingEl, isActive: !existingEl.isActive },
    ...moduleConfigs.value.slice(existingIndex + 1)
  ];
}
</script>

<style scoped>
.d-vergabe-einheiten__filter {
  max-width: 15rem;
}
:deep(.v-expansion-panel-text__wrapper) {
  padding: 0;
}
</style>
