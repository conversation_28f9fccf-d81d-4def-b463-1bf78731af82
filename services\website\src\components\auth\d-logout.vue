<template>
  <d-session-status is-loading>
    {{ t('common.redirectMessage') }}
  </d-session-status>
</template>

<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
import DSessionStatus from '@/components/auth/d-session-status.vue';
import { useAuth } from '@/service/auth/use-auth';

const props = defineProps<{
  redirectURL?: string;
}>();

const { t } = useI18n();
const { triggerLogout } = useAuth();

triggerLogout(props.redirectURL).then(); //TODO: hier könnte man nochmal den error case loggen. ist aber sehr unwahrscheinlich
</script>

<style scoped></style>
