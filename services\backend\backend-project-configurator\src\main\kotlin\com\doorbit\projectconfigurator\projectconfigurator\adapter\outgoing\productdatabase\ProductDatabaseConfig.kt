package com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.productdatabase

object ProductDatabaseConfig {

    val BASE_ID = "appunNKatsJ9mHC4Y"
    val TABLE_ID_VERGABEEINHEIT = "tblx3zs7EyIXqu97l"

    val VIEW_ID_VERGABEEINHEIT = "viwAcNGTBb7XHZz5A"
    val TABLE_ID_KOMPONENTEN = "tbl3DQ8ZMP59yzRhY"

    val VIEW_ID_KOMPONENTEN = "viw7ZhXY7psELYrZn"
    val TABLE_ID_LEISTUNG = "tblAjLT3kpyfjQbiE"

    val VIEW_ID_LEISTUNG = "viwtbfDmc8UNdf7l5"
    val TABLE_ID_LEISTUNGSPOSITIONEN = "tblQ8m1SISz5mbkTX"

    val VIEW_ID_LEISTUNGSPOSITIONEN = "viwEHlhIyjiZTXv8U"

    val TABLE_ID_CONFIG: String = "tblg42KHUjECGdlld"
    val VIEW_ID_CONFIG: String = "viwGRedhhFA9FqexD"

}
