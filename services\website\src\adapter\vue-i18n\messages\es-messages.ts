import { DefineLocaleMessage } from 'vue-i18n';

const esMessages: DefineLocaleMessage = {
  $vuetify: {
    input: {
      clear: 'Borrar'
    },
    carousel: {
      prev: 'Imagen Anterior',
      next: 'Imagen Siguiente',
      ariaLabel: {
        delimiter: '-'
      }
    },
    open: 'Abrir',
    close: 'Cerrar',
    badge: 'We dont need no stinkin badges!',
    dataFooter: {
      pageText: 'Página',
      itemsPerPageText: 'Elementos por página'
    },
    noDataText: 'Sin Datos'
  },
  errorViews: {
    notFound: '404 - Página no encontrada',
    genericError: 'Vaya, ha ocurrido algo inesperado...',
    apolloError: 'Error Servidor',
    reload: 'Recargar',
    consultAdmin: 'Póngase en contacto con el administrador',
    missingListingId: 'Falta Listing-ID en la URL',
    projectDataNotLoaded: 'No se han podido cargar los datos del proyecto',
    serverErrorLeistungCreate: 'Error del servidor, no se ha podido crear el servicio',
    noMassedaten: 'No hay Datos Medidos disponibles para el Listing-ID'
  },
  domain: {
    massedaten: 'Datos Medidos',
    preSelection: 'Preselección',
    notes: 'Notas',
    vergabeEinheitenFilters: {
      searchLabel: 'Busca',
      filterLabel: 'Filtro',
      filterItems: {
        selected: 'Seleccionados',
        invalid: 'Incorrectos',
        incomplete: 'Incompletos',
        configured: 'Configurado',
        partiallyConfigured: 'Parcialmente configurado',
        notConfigured: 'No configurado'
      }
    },
    costOverview: 'Resumen de Costos',
    globallyAvailable: {
      globallyAvailable: 'Disponible en todas las configuraciones',
      notGloballyAvailable: 'Sólo disponible en la configuración actual'
    },
    displayName: 'Nombre para mostrar',
    amount: 'Cantidad',
    priceGross: 'Precio (Bruto)',
    unitPrice: 'Precio unitario',
    securitySurcharge: 'Recargo de seguridad',
    margin: 'Margen',
    offerPriceNet: 'Precio de oferta (Neto)',
    vatAmount: 'Importe del iva',
    vatRate: 'Tipo de iva',
    netCosts: 'Costes del proyecto',
    totalProjectCosts: 'Coste total del proyecto',
    offerPriceGross: 'Precio de oferta (Bruto)',
    calculationFormula: 'Fórmula de cálculo',
    contractorName: 'Renaldo',
    dataSource: {
      dataSource: 'Fuente de Datos',
      created: 'Creado',
      imported: 'Importado/AirTable'
    },
    selfService: 'Autoservicio',
    eligible: 'Elegible',
    notEligible: 'No Elegible',
    dataSheet: 'Hoja Datos',
    execution: 'Ejecución',
    vergabeEinheit: {
      component: 'Componente',
      grossQuotePrice: 'Precio de oferta bruto',
      netPlanningCosts: 'Costes netos de planificación'
    },
    leistung: {
      selected: 'Servicio Seleccionado',
      notSelected: 'Servicio no seleccionado',
      newLeistung: 'Crear un nuevo servicio',
      leistung: 'Servicio',
      uValue: 'Valor U',
      manufacturer: 'Fabricante',
      offerPriceGross: 'Precio de oferta (bruto)',
      offerPrice: 'Precio de oferta',
      leistungType: 'Tipo de servicio',
      unit: 'Unidad',
      types: {
        ZERO_VARIANT: 'Variante cero',
        ALTERNATIVE: 'Alternativa',
        ADD_ON: 'Complemento'
      }
    },
    leistungsposition: {
      takeFromMassedaten: 'Pulse para fijar la cantidad',
      creationInstruction:
        'Todos los atributos son opcionales. Los valores por defecto se establecen para los campos vacíos.',
      unitCreateOrSelect: 'Unidad (crear o seleccionar)',
      leistungspositionen: 'Articulo de servicio',
      newLeistungsposition: 'Nuevo articulos de servicio',
      description: 'Descripción',
      unitPrice: 'Precio unitario',
      dinNumber: 'Número DIN'
    },
    configurations: {
      archiveConfirm: '¿Realmente quieres borrar la configuración?',
      createNewConfig: 'Crear nueva configuración',
      createNewError: 'No se pudo crear la configuración',
      databaseVersion: 'Versión de la base de datos',
      newDatabaseVersion: 'Crear nueva versión de base de datos',
      configLabel: 'Etiqueta',
      noneCreated: 'No hay configuraciones creadas todavía',
      createFirst: 'Crea tu primera configuración haciendo clic en + arriba',
      noDbVersions: 'No hay versiones de BD disponibles'
    }
  },
  common: {
    ok: 'OK',
    yes: 'Si',
    no: 'No',
    new: 'Nuevo',
    save: 'Guardar',
    cancel: 'Cancelar',
    delete: 'Borrar',
    prev: 'Anterior',
    next: 'Siguiente',
    download: 'Descargar',
    redirectMessage: 'Serás redirigido en breve …',
    pleaseWait: 'Espere por favor',
    back: 'Volver'
  },
  formRules: {
    required: 'Falta información'
  }
};

export default esMessages;
