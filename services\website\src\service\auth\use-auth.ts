import { App, inject, InjectionKey } from 'vue';
import { AUTH_SERVICE } from './AuthService';
import { Auth } from '@/service/auth/Auth';

const AuthInjectionKey = Symbol('auth') as InjectionKey<Auth>;

export function initializeAuth(app: App<Element>) {
  app.provide<Auth>(AuthInjectionKey, {
    isAuthInitialized: AUTH_SERVICE.isInitialized,
    isLoggedIn: AUTH_SERVICE.isLoggedIn,
    username: AUTH_SERVICE.username,
    userId: AUTH_SERVICE.userId,
    accessToken: AUTH_SERVICE.accessToken,
    isAdminUser: AUTH_SERVICE.isAdminUser,
    ísPremiumUser: AUTH_SERVICE.isPremiumUser,

    triggerRegistration: (redirectURL) => AUTH_SERVICE.triggerRegistration(redirectURL),
    triggerLogin: (redirectURL) => AUTH_SERVICE.triggerLogin(redirectURL),
    triggerPasswordChange: (redirectURL) => AUTH_SERVICE.triggerPasswordChange(redirectURL),
    triggerLogout: (redirectURL) => AUTH_SERVICE.triggerLogout(redirectURL),
    openAccountManagement: () => AUTH_SERVICE.openAccountManagement(),
    fetchUserProfile: () => AUTH_SERVICE.userProfile()
  });
}

export function useAuth(): Auth {
  return inject<Auth>(AuthInjectionKey)!;
}
