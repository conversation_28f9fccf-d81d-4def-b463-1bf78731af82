package com.doorbit.projectconfigurator.projectconfigurator.domain.applicationservice

import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.LeistungCreateDto
import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.LeistungsPositionCreateDto
import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.ProjectConfiguratorApi
import com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.listing.ListingApiAdapter
import com.doorbit.projectconfigurator.projectconfigurator.domain.LeistungId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProductDatabaseId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProjectConfigurationId
import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.WithLogger
import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.i
import com.doorbit.projectconfigurator.projectconfigurator.domain.massedaten.Massedatum
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.ProductDatabase
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.ProductDatabaseImportService
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.SchnellkonfigurationType
import com.doorbit.projectconfigurator.projectconfigurator.domain.project.Project
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.LeistungsPositionId
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.ProjectConfiguration
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.SelectedLeistung
import com.doorbit.projectconfigurator.projectconfigurator.repository.ProductDatabaseRepository
import com.doorbit.projectconfigurator.projectconfigurator.repository.ProjectConfigurationRepository
import com.doorbit.projectconfigurator.projectconfigurator.repository.ProjectRepository
import org.bson.types.ObjectId
import org.springframework.stereotype.Service
import java.lang.Thread.startVirtualThread
import java.math.BigDecimal

@Service
class ProjectConfiguratorService(
    private val productDatabaseImportService: ProductDatabaseImportService,
    private val productDatabaseRepository: ProductDatabaseRepository,
    private val projectConfigurationRepository: ProjectConfigurationRepository,
    private val projectRepository: ProjectRepository,
    private val listingApiAdapter: ListingApiAdapter
) : ProjectConfiguratorApi {

    override fun loadProductDatabase(projectConfigurationId: ProjectConfigurationId): ProductDatabase {
        val pc = projectConfigurationRepository.findById(projectConfigurationId)
        return productDatabaseRepository.findById(pc.productDatabaseId)
    }

    override fun importProductDatabase(versionName: String): ProductDatabaseId = productDatabaseImportService.importProductDatabase(versionName)

    override fun getDatabaseVersions(): List<ProductDatabase> {
        return productDatabaseRepository.databaseVersions()
    }

    override fun getModuleNames(databaseVersion: String): List<String> {
        return productDatabaseRepository.findDistinctModuleNames(databaseVersion)
    }

    override fun createConfiguration(projectId: String, productDatabaseId: ProductDatabaseId, name: String, schnellkonfigurationType: SchnellkonfigurationType?): ProjectConfigurationId {

        LOGGER.i { "Creating configuration with productDatabaseId $productDatabaseId and name $name" }

        val productDatabase = productDatabaseRepository.findById(productDatabaseId)

        val projectConfiguration = ProjectConfiguration(
            id = ObjectId(),
            name = name,
            productDatabaseId = productDatabaseId,
            databaseVersion = productDatabase.versionName,
        )

        if (schnellkonfigurationType != null) {
            productDatabase.allLeistungenWithSchnellKonfiguration(schnellkonfigurationType).forEach { leistung ->
                projectConfiguration.leistungen.selectLeistung(leistung.recordId, true)
                projectConfiguration.selectAllLeistungsPositionen(leistung.recordId, dbLeistung = leistung)
            }

            projectConfiguration.calculate()
        }

        projectRepository.addConfigurationId(projectId, projectConfiguration.id)
        projectConfigurationRepository.save(projectConfiguration)

        return projectConfiguration.id
    }

    override fun copyConfiguration(projectId: String, configurationId: ProjectConfigurationId, name: String): ProjectConfigurationId {
        val original = projectConfigurationRepository.findById(configurationId)
        val copy = original.copy(id = ObjectId(), name = name)

        projectRepository.addConfigurationId(projectId, copy.id)
        projectConfigurationRepository.save(copy)

        return copy.id
    }

    override fun getProjectConfiguration(configurationId: ProjectConfigurationId): ProjectConfiguration {
        return projectConfigurationRepository.findById(configurationId)
    }

    override fun selectLeistung(configurationId: ProjectConfigurationId, leistungId: LeistungId, selection: Boolean) {
        LOGGER.i { "Selecting Leistung with id $leistungId for config $configurationId" }

        val pc = projectConfigurationRepository.findById(configurationId)

        pc.leistungen.selectLeistung(leistungId, selection)
        if (selection) {
            pc.selectAllLeistungsPositionen(leistungId, productDatabaseRepository.findById(pc.productDatabaseId).leistung(leistungId))
        }

        pc.calculate()

        projectConfigurationRepository.save(pc)

        // Cache warming
        startVirtualThread { projectConfigurationRepository.findById(pc.id) }
    }

    override fun selectLeistungsPosition(configurationId: ProjectConfigurationId, leistungId: LeistungId, leistungsPositionId: LeistungsPositionId, selection: Boolean) {
        LOGGER.i { "Selecting LeistungsPosition with id $leistungsPositionId for config $configurationId" }

        val pc = projectConfigurationRepository.findById(configurationId)
        val db = productDatabaseRepository.findById(pc.productDatabaseId)
        val dbLeistung = db.leistung(leistungId)
        pc.leistungen.selectLeistungsPosition(leistungId, leistungsPositionId, selection, dbLeistung)
        pc.calculate()

        projectConfigurationRepository.save(pc)

        // Cache warming
        startVirtualThread { projectConfigurationRepository.findById(pc.id) }
    }

    override fun changeExecution(configurationId: ProjectConfigurationId, leistungId: LeistungId, leistungExecutionType: SelectedLeistung.LeistungExecutionType) {
        LOGGER.i { "Changing execution for Leistung with id $leistungId for config $configurationId to ${leistungExecutionType.name}" }

        val pc = projectConfigurationRepository.findById(configurationId)

        pc.leistungen.changeExecution(leistungId, leistungExecutionType)
        pc.calculate()

        projectConfigurationRepository.save(pc)

        // Cache warming
        startVirtualThread { projectConfigurationRepository.findById(pc.id) }
    }

    override fun createLeistung(configurationId: ProjectConfigurationId, komponenteId: String, payload: LeistungCreateDto) {
        val pc = projectConfigurationRepository.findById(configurationId)
        val db = productDatabaseRepository.findById(pc.productDatabaseId)
        val (updatedDb, createdLeistung) = db.createLeistung(komponenteId, payload)

        productDatabaseRepository.save(updatedDb)
        projectConfigurationRepository.save(pc.addCustomLeistungId(createdLeistung.recordId))

        // Cache warming
        startVirtualThread { projectConfigurationRepository.findById(pc.id) }
    }

    override fun createLeistungsPosition(configurationId: ProjectConfigurationId, leistungId: LeistungId, dto: LeistungsPositionCreateDto) {
        val pc = projectConfigurationRepository.findById(configurationId)
        val db = productDatabaseRepository.findById(pc.productDatabaseId)
        val (updatedDb, createdLeistungsposition) = db.createLeistungsPosition(leistungId, dto)

        productDatabaseRepository.save(updatedDb)
        val updatedPc = pc.addCustomLeistungsPositionId(createdLeistungsposition.recordId)
        projectConfigurationRepository.save(updatedPc)

        configureLeistungsPosition(configurationId, leistungId, createdLeistungsposition.recordId, dto.unitPrice, dto.vatRatePercent, dto.amount, dto.marginPercent, dto.securitySurchargePercent, dto.notes)
    }

    override fun configureLeistungsPosition(configurationId: ProjectConfigurationId, leistungId: LeistungId, leistungsPositionId: LeistungsPositionId, unitPrice: BigDecimal?, vatRatePercent: BigDecimal?, amount: BigDecimal?, margin: BigDecimal?, securitySurcharge: BigDecimal?, notes: String?) {
        val pc = projectConfigurationRepository.findById(configurationId)
        val db = productDatabaseRepository.findById(pc.productDatabaseId)
        val dbLeistung = db.leistung(leistungId)

        pc.leistungen.configureLeistungsPosition(leistungId, leistungsPositionId, unitPrice, vatRatePercent, amount, margin, securitySurcharge, notes, dbLeistung)
        pc.calculate()

        projectConfigurationRepository.save(pc)

        // Cache warming
        startVirtualThread { projectConfigurationRepository.findById(pc.id) }
    }

    override fun findProjectById(id: ObjectId): Project? {
        return projectRepository.findById(id)
    }

    override fun findProjectByListingId(listingId: String): Project {
        return projectRepository.findOrCreateProjectByListingId(listingId, createIfMissing = true)!!
    }

    override fun createProject(listingId: String): Project {
        return projectRepository.createProject(listingId)
    }

    override fun getMassedaten(projectId: ObjectId): Map<String, Massedatum>? {
        val listingId = projectRepository.findById(projectId)!!.listingId
        return listingApiAdapter.getMassedaten(listingId)
    }

    override fun archiveConfiguration(configurationId: ProjectConfigurationId) {
        val pc = projectConfigurationRepository.findById(configurationId)
        projectConfigurationRepository.save(pc.setArchived())
    }

    override fun deleteLeistung(configurationId: ObjectId, leistungId: LeistungId) {
        val pc = projectConfigurationRepository.findById(configurationId)

        val updatedPc = pc.deleteLeistung(leistungId)

        projectConfigurationRepository.save(updatedPc)
    }

    override fun deleteLeistungsPosition(configurationId: ObjectId, leistungId: LeistungId, leistungsPositionId: LeistungsPositionId) {
        val pc = projectConfigurationRepository.findById(configurationId)

        val updatedPc = pc.deleteLeistungsPosition(leistungId, leistungsPositionId)

        projectConfigurationRepository.save(updatedPc)
    }

    companion object : WithLogger()
}