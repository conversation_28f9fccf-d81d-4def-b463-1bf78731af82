package com.doorbit.projectconfigurator.bff.infra.api.dto

import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.ProductDatabase

object LeistungsPositionDefaultsMapper {
    fun toLeistungsPositionDefaultsDto(productDatabase: ProductDatabase): LeistungsPositionDefaults {
        val config = productDatabase.config
        return LeistungsPositionDefaults(
            vatRatePercent = config.vatRatePercent.toDouble(),
            marginPercent = config.defaultMarginPercent.toDouble(),
            securitySurchargePercent = config.defaultSecuritySurchargePercent.toDouble(),
            existingUnits = productDatabase.allDistinctUnits
        )
    }
}