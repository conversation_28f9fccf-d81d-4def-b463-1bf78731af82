import { createApp } from 'vue';
import vuetify from '@/adapter/vuetify/vuetify';
import vueRouter from '@/adapter/vue-router/vue-router';
import vueApollo from '@/adapter/graphql/vue-apollo';
import vueI18n from '@/adapter/vue-i18n/vue-i18n';
import { IS_DEVELOPMENT } from '@/utility/environment';
import DLayoutDesktop from '@/components/layout/d-layout-desktop.vue';
import { initializeAuth } from '@/service/auth/use-auth';
import '@fontsource/roboto/100.css';
import '@fontsource/roboto/300.css';
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/500.css';
import '@fontsource/roboto/700.css';
import '@fontsource/roboto/900.css';
import '@fontsource/montserrat/500.css';
import '@fontsource/montserrat/400.css';
import '@fontsource/montserrat/300.css';
import { initializeLocalStorage } from '@/service/local-storage/local-storage';
import { formRules, formRulesKey } from '@/utility/form-rules';

const app = createApp(DLayoutDesktop);

app.config.performance = IS_DEVELOPMENT;

initializeLocalStorage(app);
initializeAuth(app);

app.use(vueI18n);
app.use(vueRouter);
app.use(vueApollo);
app.use(vuetify);
app.provide(formRulesKey, formRules);

app.mount('#app');
