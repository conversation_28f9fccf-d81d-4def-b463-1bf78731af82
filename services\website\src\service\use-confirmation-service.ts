import { inject, InjectionKey, provide, ref } from 'vue';

export type ConfirmationService = ReturnType<typeof initializeConfirmationService>;

export interface ConfirmationForm {
  message: string;
  cancelText?: string;
  confirmText?: string;
  confirm: () => void;
}

const confirmationServiceKey = Symbol('confirmationService') as InjectionKey<ConfirmationService>;

export function initializeConfirmationService(provideFn: typeof provide) {
  const formData = ref<ConfirmationForm | null>();
  const confirmationService = { formData };
  provideFn<ConfirmationService>(confirmationServiceKey, confirmationService);
  return confirmationService;
}

export function useConfirmationService(): ConfirmationService {
  return inject<ConfirmationService>(confirmationServiceKey)!;
}
