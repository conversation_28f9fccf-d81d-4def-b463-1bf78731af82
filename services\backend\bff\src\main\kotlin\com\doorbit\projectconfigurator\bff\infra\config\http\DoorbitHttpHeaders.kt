package com.doorbit.projectconfigurator.bff.infra.config.http

object DoorbitHttpHeaders {
    /**
     * Unique ID des Requests. Ist immer nur zusammen mit der User-ID wirklich unique.
     */
    const val RequestId = "X-Request-ID"

    /**
     * Listing ID des Requests, sofern es um Operationen zu einem bestimmten Listing geht.
     */
    const val ListingId = "X-Listing-ID"

    /**
     * User ID.
     */
    const val UserId = "X-User-ID"

    /**
     * ID des aufrufenden Clients. Kann entweder 'Website' oder 'App' sein.
     */
    const val ClientId = "X-Client-Id"

    /**
     * Versionsnummer des aufrufenden Clients.
     */
    const val ClientVersion = "X-Client-Version"

    /**
     * Kontext des aufrufenden Clients. Bei 'Website' ist dies die URL der Seite, auf der der Nutzer sich befindet, in der App die aktuelle View / Component.
     */
    const val ClientContext = "X-Client-Context"

    /**
     * Sprache des aufrufenden Clients.
     */
    const val ClientLocale = "X-Client-Locale"

    /**
     * Gerät des aufrufenden Clients.
     */
    const val ClientDevice = "X-Client-Device"

    /**
     * Betriebssystem des aufrufenden Clients.
     */
    const val ClientDeviceOsVersion = "X-Client-Device-OS-Version"

    /**
     * Gibt an, ob dem Nutzer Demo-Inserate/Features angezeigt werden sollen.
     */
    const val DemoMode = "X-Demo"

    /**
     * Gibt an, ob der Request von einem Test-System kommt.
     */
    const val TestRequest = "X-Testing"

    /**
     * Token to save and delete listing building
     */
    const val ListingBuildingToken = "X-Listing-Building-Token"
}
