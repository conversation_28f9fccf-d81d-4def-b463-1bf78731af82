package com.doorbit.projectconfigurator.bff.infra.api

import com.doorbit.projectconfigurator.bff.core.domain.extension.WithLogger
import com.doorbit.projectconfigurator.bff.core.domain.extension.d
import com.doorbit.projectconfigurator.bff.infra.adapter.ProjectConfiguratorApiAdapter
import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.MutationMapping
import org.springframework.stereotype.Controller

@Controller
class ProjectMutations(
    private val projectConfiguratorApiAdapter: ProjectConfiguratorApiAdapter
) {

    @MutationMapping
    fun createProject(@Argument listingId: String): String {
        LOGGER.d { "createProject: listingId=$listingId"}
        return projectConfiguratorApiAdapter.createProject(listingId).id.toHexString()
    }

    companion object : WithLogger()

}