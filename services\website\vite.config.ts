import vue from '@vitejs/plugin-vue';
import vuetify, { transformAssetUrls } from 'vite-plugin-vuetify';
import { defineConfig, loadEnv } from 'vite';
import { fileURLToPath } from 'url';
import { URL } from 'node:url';
import fs from 'node:fs';
import path from 'node:path';

// https://vitejs.dev/config/
// https://vitejs.dev/config/#using-environment-variables-in-config
export default defineConfig(
  ({
    // command,
    mode
  }) => {
    // const env =
    loadEnv(mode, process.cwd(), '');

    if (mode === 'dev') {
      const filePath = path.resolve(__dirname, '../../devsetup/data/.test-configuration-id');
      const configId = fs.readFileSync(filePath, 'utf8').trim();
      if (!configId) throw Error('No value in devsetup/data/.test-configuration-id');
      process.env.VITE_TEST_CONFIGURATION_ID = configId;
    } else {
      process.env.VITE_TEST_CONFIGURATION_ID = '668bfa6212d74f6d31200ef2';
    }
    console.log(`Set env: VITE_TEST_CONFIGURATION_ID=${process.env.VITE_TEST_CONFIGURATION_ID}`);

    return {
      build: {
        cssMinify: true,
        minify: true
      },
      plugins: [
        vue({
          template: {
            transformAssetUrls
          }
        }),
        // https://github.com/vuetifyjs/vuetify-loader/tree/next/packages/vite-plugin
        vuetify({
          autoImport: true
        })
      ],
      define: {
        'process.env': {}
      },
      resolve: {
        alias: {
          '@': fileURLToPath(new URL('./src', import.meta.url))
        },
        extensions: ['.js', '.json', '.jsx', '.mjs', '.ts', '.tsx', '.vue']
      },
      server: {
        port: 8088,
        headers: {}
      }
    };
  }
);
