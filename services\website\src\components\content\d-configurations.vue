<template>
  <v-dialog
    max-width="400"
    :model-value="!!currentlyEditedConfiguration"
    @update:model-value="currentlyEditedConfiguration = null"
  >
    <v-card v-if="currentlyEditedConfiguration">
      <v-card-title>{{ currentlyEditedConfiguration.configurationName }}</v-card-title>
      <v-card-text>
        <div>{{ currentlyEditedConfiguration.configurationId }}</div>
        <div>
          {{ t('domain.configurations.databaseVersion') }}: {{ currentlyEditedConfiguration.databaseVersion.version }}
        </div>
      </v-card-text>
      <v-card-actions>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="grey" :text="t('common.cancel')" @click="currentlyEditedConfiguration = null" />
          <v-btn
            color="red"
            :text="t('common.delete')"
            @click="
              formData = {
                message: t('domain.configurations.archiveConfirm'),
                confirm: archiveSelectedConfig
              }
            "
          />
          <!--          <v-btn color="primary" variant="tonal" tabindex="0" type="submit" disabled text="Duplizieren" />-->
        </v-card-actions>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <v-tabs v-model="selectedConfigIndex" color="black" show-arrows center-active>
    <v-tab v-for="(configuration, index) in configurations" :key="index" :rounded="false" slider-color="primary">
      {{ configuration.configurationName }}
      <v-btn
        v-if="displayContext === 'BACKOFFICE' && index === selectedConfigIndex"
        class="ml-2"
        variant="text"
        color="grey"
        height="2rem"
        width="2rem"
        :icon="mdiCog"
        @click.stop="currentlyEditedConfiguration = configuration"
      />
      <v-tooltip v-if="displayContext === 'BACKOFFICE'" activator="parent" location="bottom">
        <div class="text-subtitle-1">{{ t('domain.configurations.databaseVersion') }}:</div>
        <div class="text-subtitle-2 text-pre-wrap">
          {{ configuration.databaseVersion.version }}
        </div>
        <div class="text-subtitle-2 text-pre-wrap">({{ configuration.databaseVersion.databaseId }})</div>
        <div class="text-subtitle-2 text-pre-wrap">
          {{ new Date(configuration.databaseVersion.createdAt).toLocaleDateString() }}
          {{ new Date(configuration.databaseVersion.createdAt).toLocaleTimeString() }}
        </div>
      </v-tooltip>
    </v-tab>
  </v-tabs>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { mdiCog } from '@mdi/js';
import { useI18n } from 'vue-i18n';
import { ConfigurationEntity, useProjectService } from '@/service/use-project-service';
import { useDArchiveConfigMutation } from '@/adapter/graphql/generated/graphql';
import { useConfirmationService } from '@/service/use-confirmation-service';

const { t } = useI18n();
const selectedConfigIndex = ref<number | null>(null);
const currentlyEditedConfiguration = ref<ConfigurationEntity | null>(null);
const { configurations, selectedConfiguration, displayContext } = useProjectService();
const { mutate: archiveConfig } = useDArchiveConfigMutation();
const { formData } = useConfirmationService();

watch(configurations, (newConfigs) => {
  if (typeof selectedConfigIndex.value !== 'number' && newConfigs.length) {
    selectedConfigIndex.value = 0;
  }
});

watch(
  selectedConfigIndex,
  (i) => {
    selectedConfiguration.value = typeof i === 'number' ? configurations.value[i] : null;
  },
  { immediate: true }
);

async function archiveSelectedConfig() {
  if (selectedConfiguration.value) {
    await archiveConfig({ configurationId: selectedConfiguration.value.configurationId });
    configurations.value = configurations.value.filter(
      (existingConfig) => existingConfig.configurationId !== selectedConfiguration.value?.configurationId
    );
    selectedConfigIndex.value = configurations.value.length ? 0 : null;
  }
  formData.value = null;
  currentlyEditedConfiguration.value = null;
}
</script>

<style scoped></style>
