<template>
  <v-card>
    <v-card-text>{{ message }}</v-card-text>
    <v-card-actions>
      <v-spacer></v-spacer>
      <v-btn color="grey" @click="emits('finish')">{{ cancelText ?? t('common.cancel') }}</v-btn>
      <v-btn @click="confirm">{{ confirmText ?? t('common.yes') }}</v-btn>
    </v-card-actions>
  </v-card>
</template>
<script setup lang="ts">
import { ConfirmationForm } from '@/service/use-confirmation-service';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const emits = defineEmits<{ finish: [] }>();
defineProps<ConfirmationForm>();
</script>
