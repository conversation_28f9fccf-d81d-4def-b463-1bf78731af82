package com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing

import com.slack.api.Slack
import com.slack.api.webhook.Payload
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class SlackAdapter(
    @Value(value = "\${application.slack.enabled}") private val slackEnabled : <PERSON><PERSON><PERSON>,
    @Value(value = "\${application.slack.webhook}") private val slackWebhookUrl : String
) {

    fun postMessage(message: String) {
        if (slackEnabled) {
            // post message to slack
            Thread.startVirtualThread {
                val payload = Payload.builder().text(message).build()
                Slack.getInstance().send(slackWebhookUrl, payload)
            }
        }
    }

}