import { CompletenessState } from '@/adapter/graphql/generated/graphql';

export const FILTER_COMPLETELY_CONFIGURED = '123cnfgrd';
export const FILTER_NOT_CONFIGURED = '123ntcnfgrd';
export const FILTER_PARTIALLY_CONFIGURED = '123prtcnfgrd';
export const FILTER_HAS_WARNINGS = '123hswrng';
export const FILTER_IS_SELECTED = '123slctd';

export function mapCompletenessToToken(completeness?: CompletenessState): string {
  switch (completeness) {
    case 'PARTIALLY_CONFIGURED':
      return FILTER_PARTIALLY_CONFIGURED;
    case 'COMPLETELY_CONFIGURED':
      return FILTER_COMPLETELY_CONFIGURED;
    case 'NOT_CONFIGURED':
      return FILTER_NOT_CONFIGURED;
    default:
      return '';
  }
}

export function mapWarningToToken(val?: boolean): string {
  return val ? FILTER_HAS_WARNINGS : '';
}
export function mapSelectionToToken(val?: boolean): string {
  return val ? FILTER_IS_SELECTED : '';
}
