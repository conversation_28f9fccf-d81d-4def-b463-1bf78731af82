<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="ConsoleLoggerStdOut" class="ch.qos.logback.core.ConsoleAppender">
        <!-- dev, test and other non-prod spring profiles log HR text to console (1 Log, 1 line) -->
        <springProfile name="!live &amp; !integ">
            <layout class="ch.qos.logback.classic.PatternLayout">
                <Pattern>%d{HH:mm:ss.SSS} %highlight(%-5level) %logger{50} | %gray(%M) %-3line - %msg%n%rEx</Pattern>
            </layout>
        </springProfile>

        <!-- prod spring profile logs JSON instead of String -->
        <springProfile name="integ | live">
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
                <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                    <!-- DP shows first stack trace first, this should be the root cause -->
                    <rootCauseFirst>true</rootCauseFirst>
                </throwableConverter>
                <fieldNames>
                    <timestamp>timestamp</timestamp>
                    <level>severity</level>
                    <message>message</message>
                    <thread>thread</thread>
                    <logger>logger</logger>
                    <stackTrace>stack_trace</stackTrace>
                    <mdc>mdc</mdc>
                    <version>[ignore]</version>
                    <levelValue>[ignore]</levelValue>
                </fieldNames>
            </encoder>
        </springProfile>
    </appender>

    <root level="INFO">
        <appender-ref ref="ConsoleLoggerStdOut"/>
    </root>
</configuration>
