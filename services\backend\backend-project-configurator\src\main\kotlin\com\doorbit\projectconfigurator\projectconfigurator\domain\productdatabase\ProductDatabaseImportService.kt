package com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase

import com.doorbit.projectconfigurator.projectconfigurator.domain.ProductDatabaseId
import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.WithLogger
import com.doorbit.projectconfigurator.projectconfigurator.domain.extension.i
import com.doorbit.projectconfigurator.projectconfigurator.repository.ProductDatabaseRepository
import org.springframework.stereotype.Service

@Service
class ProductDatabaseImportService(
    private val productDatabaseImporter: ProductDatabaseImporter,
    private val productDatabaseRepository: ProductDatabaseRepository
) {

    fun importProductDatabase(versionName: String): ProductDatabaseId {
        LOGGER.i { "Importing product database from Airtable" }

        productDatabaseRepository.assertVersionNameIsUnique(versionName)
        val db = productDatabaseImporter.importProductDatabase(versionName)

        productDatabaseRepository.insert(db.id, db)

        LOGGER.i { "Successfully imported new product database from Airtable. New Id: ${db.id}" }

        return db.id
    }

    companion object : WithLogger()

}