export function getTranslation(locale: string, data?: { languageCode: string; translation: string }[] | null) {
  if (!data?.length) return '';
  for (const { languageCode, translation } of data) {
    if (locale === languageCode) {
      return translation;
    }
    if (locale.startsWith(languageCode)) {
      // covering de-DE etc
      return translation;
    }
  }
  return data[0].translation || '';
}
