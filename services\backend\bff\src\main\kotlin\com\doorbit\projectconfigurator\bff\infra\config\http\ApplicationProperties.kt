package com.doorbit.projectconfigurator.bff.infra.config.http

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = "application")
data class ApplicationProperties(
    var cache: Map<String, CacheDetailConfig> = emptyMap(),
)

class CacheDetailConfig(
    var ttlInMinutes: Long,
    var maxSize: Long
)
