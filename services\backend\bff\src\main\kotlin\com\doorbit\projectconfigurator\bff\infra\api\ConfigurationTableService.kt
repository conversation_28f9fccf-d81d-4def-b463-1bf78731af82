package com.doorbit.projectconfigurator.bff.infra.api

import com.doorbit.projectconfigurator.bff.infra.adapter.ProjectConfiguratorApiAdapter
import com.doorbit.projectconfigurator.bff.infra.api.dto.ConfigurationTable
import com.doorbit.projectconfigurator.bff.infra.api.dto.ConfigurationTableMapper
import com.doorbit.projectconfigurator.bff.infra.api.dto.DisplayContext
import org.bson.types.ObjectId
import org.springframework.stereotype.Service

@Service
class ConfigurationTableService(
    private val projectConfiguratorApiAdapter: ProjectConfiguratorApiAdapter
) {
    /**
     * Frontend orchestrator, note that this method returns a Frontend-API DTO.
     */
    fun findConfigurationTable(configurationId: String, displayContext: DisplayContext): ConfigurationTable? {
        val configId = ObjectId(configurationId)
        val db = projectConfiguratorApiAdapter.getProductDatabase(configId)
        val pc = projectConfiguratorApiAdapter.getProjectConfiguration(configId)

        if (pc.archived) {
            return null
        }

        return ConfigurationTableMapper(displayContext).toDto(db, pc)
    }
}