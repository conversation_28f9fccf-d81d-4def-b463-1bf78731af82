<template>
  <v-tabs color="black" slider-color="transparent" direction="vertical">
    <v-tab
      v-for="vergabeEinheit in vergabeEinheiten"
      :key="vergabeEinheit.id"
      :rounded="false"
      @click="emits('id-selected', vergabeEinheit.id)"
    >
      <template #prepend>
        <d-vergabe-einheit-icon :completeness-state="vergabeEinheit.completenessState" />
      </template>
      <span class="pa-1" :class="[getVergabeEinheitBgColor(vergabeEinheit.completenessState)]">{{
        getTranslation(locale, vergabeEinheit.displayName)
      }}</span>
    </v-tab>
  </v-tabs>
</template>

<script setup lang="ts">
import { VergabeEinheit } from '@/adapter/graphql/generated/graphql';
import { getTranslation } from '@/utility/get-translation';
import { getVergabeEinheitBgColor } from '@/utility/get-vergabe-einheit-bg-color';
import DVergabeEinheitIcon from '@/components/content/d-vergabe-einheit-icon.vue';
import { useI18n } from 'vue-i18n';

const { locale } = useI18n();
const emits = defineEmits<{ 'id-selected': [id: string] }>();
defineProps<{
  vergabeEinheiten: VergabeEinheit[];
}>();
</script>

<style scoped>
:deep(.v-tab) {
  letter-spacing: normal;
  text-transform: none !important;
  font-weight: normal;
}
:deep(.v-tab--selected) {
}
</style>
