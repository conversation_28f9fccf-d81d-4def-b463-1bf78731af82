package com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase

import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.LeistungCreateDto
import com.doorbit.projectconfigurator.projectconfigurator.adapter.incoming.LeistungsPositionCreateDto
import com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.productdatabase.DbColumnConstants
import com.doorbit.projectconfigurator.projectconfigurator.domain.LeistungId
import com.doorbit.projectconfigurator.projectconfigurator.domain.ProductDatabaseId
import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException
import java.math.BigDecimal
import java.time.Instant

data class ProductDatabase(
    val id: ProductDatabaseId,
    val versionName: String,
    val importedAt: Instant = Instant.now(),
    val module: List<DbModul>,
    val config: DbConfig,
    var allDistinctUnits: List<String> = emptyList()
) {

    init {
        if (allDistinctUnits.isEmpty()) {
            refreshAllDistinctUnits()
        }
    }

    private fun refreshAllDistinctUnits() {
        allDistinctUnits = module.asSequence()
            .flatMap(DbModul::vergabeEinheiten)
            .flatMap(DbVergabeeinheit::komponenten)
            .flatMap(DbKomponente::dbLeistung)
            .flatMap(DbLeistung::leistungsPositionen)
            .mapNotNull { it.otherFields[DbColumnConstants.UNIT] as? String }
            .distinct().toList()
    }

    /**
     * Returns all Vergabeeinheiten.
     *
     */
    fun vergabeEinheiten(): List<DbVergabeeinheit> {
        return module.flatMap(DbModul::vergabeEinheiten)
    }

    fun komponente(komponenteId: String): DbKomponente {
        return module.asSequence().flatMap { it.vergabeEinheiten }.flatMap { it.komponenten }.first { it.recordId == komponenteId }
    }

    fun leistung(leistungId: String): DbLeistung {
        return module.asSequence().flatMap { it.vergabeEinheiten }.flatMap { it.komponenten }.flatMap { it.dbLeistung }.first { it.recordId == leistungId }
    }

    fun allLeistungenWithSchnellKonfiguration(schnellkonfigurationType: SchnellkonfigurationType): List<DbLeistung> {
        return module.asSequence()
            .flatMap { it.vergabeEinheiten }
            .flatMap { it.komponenten }
            .flatMap { it.dbLeistung }
            .filter { it.schnellkonfiguration?.contains(schnellkonfigurationType) == true }
            .toList()
    }

    fun createLeistung(komponenteId: String, payload: LeistungCreateDto): Pair<ProductDatabase, DbLeistung> {
        val dbLeistung = DbCustomTableItemMapper.map(payload)
        komponente(komponenteId).dbLeistung.add(dbLeistung)

        return this to dbLeistung
    }

    fun createLeistungsPosition(leistungId: LeistungId, dto: LeistungsPositionCreateDto): Pair<ProductDatabase, DbLeistungsposition> {
        val dbLeistungsposition = DbCustomTableItemMapper.mapLeistungsPosition(dto)
        leistung(leistungId).leistungsPositionen.add(dbLeistungsposition)

        if (!allDistinctUnits.contains(dto.unit)) {
            allDistinctUnits = allDistinctUnits + dto.unit
        }

        return this to dbLeistungsposition
    }
}

data class DbConfig(val defaultMarginPercent: BigDecimal, val defaultSecuritySurchargePercent: BigDecimal, val vatRatePercent: BigDecimal)
data class DbModul(val name: String, val vergabeEinheiten: List<DbVergabeeinheit>) {
    fun allLeistungIds(): List<String> {
        return vergabeEinheiten.flatMap { it.allLeistungIds() }
    }
}
data class DbVergabeeinheit(val recordId: String, val name: String, val komponenten: List<DbKomponente>, val modul: String) {

    fun allLeistungIds(): List<String> {
        return komponenten.flatMap { komponente -> komponente.dbLeistung.map(DbLeistung::recordId) }
    }
}

data class DbKomponente(val recordId: String, val name: String, val modul: String?, val dbLeistung: MutableList<DbLeistung>) {
    fun allLeistungIds(): List<String> {
        return dbLeistung.map(DbLeistung::recordId)
    }
}

data class DbLeistung(val recordId: String, val name: String, val type: LeistungType?, val schnellkonfiguration: List<SchnellkonfigurationType>? = null, val leistungsPositionen: MutableList<DbLeistungsposition>, val otherFields: Map<String, Any>, val customCreatedLeistung: DbCustomLeistungInfo? = null) {

    fun leistungsPosition(leistungsPositionId: String): DbLeistungsposition {
        return leistungsPositionen.firstOrNull { it.recordId == leistungsPositionId } ?: throw ResponseStatusException(HttpStatus.BAD_REQUEST, "Leistungsposition with Id $leistungsPositionId not found")
    }

}

enum class SchnellkonfigurationType {
    S,
    M,
    L
}

data class DbCustomLeistungInfo(val globallyAvailable: Boolean)
data class DbLeistungsposition(val recordId: String, val name: String, val unitPrice: BigDecimal, val vatRatePercent: BigDecimal, val marginPercent: BigDecimal, val securitySurchargePercent: BigDecimal, val otherFields: Map<String, Any>, val isCustomCreatedLeistungsPosition: Boolean = false, val defaultAmount: BigDecimal? = null)

enum class LeistungType(val value: String) {

    ALTERNATIVE("Alternative"),
    ADD_ON("Add-On"),
    ZERO_VARIANT("Nullvariante");

    companion object {
        fun fromString(value: String?): LeistungType? {
            return entries.firstOrNull { it.value == value }
        }
    }

}

enum class DbLeistungFields(val value: String) {
    NAME_LONG("Name (Lang)"),
    DESCRIPTION("Description"),
    PHOTOS("Bild"),
    FACTSHEET_URL("Factsheet-Url"),
    MANUFACTURER("Hersteller"),
    U_VALUE("U Wert"),
    UNIT("Unit"),
    OFFER_PRICE("Angebotspreis"),

    ;

    companion object {
        fun fromString(value: String?): DbLeistungFields? {
            return values().firstOrNull { it.value == value }
        }
    }

}
