import { Ref, ref, watch } from 'vue';
import { IS_DEVELOPMENT } from '@/utility/environment';

export class LocalStorageField<T> {
  public readonly state: Ref<T>;
  private readonly localStorageKey: string;

  constructor(
    public readonly id: string,
    private readonly defaultValue: T
  ) {
    this.localStorageKey = `doorbit_${id}`; //TODO anpassen
    this.state = ref<T>(defaultValue) as Ref<T>;

    this.initialize();
  }

  private deleteFromLocalStorage() {
    localStorage.removeItem(this.localStorageKey);
  }

  private initialize() {
    this.setValueFromStorage();

    watch(
      this.state,
      (newValue) => {
        if (newValue == null) {
          this.deleteFromLocalStorage();
        } else {
          this.saveToLocalStorage(newValue as T);
        }
      },
      { deep: true }
    );

    addEventListener('storage', (event) => {
      if (event.key === this.localStorageKey) {
        this.setValueFromStorage();
      }
    });
  }

  private loadFromLocalStorage(): T {
    if (IS_DEVELOPMENT) {
      console.log('load', this.id); //TODO build own logger
    }

    const rawValue = localStorage.getItem(this.localStorageKey);
    if (rawValue == null) {
      return this.defaultValue;
    }
    try {
      return JSON.parse(rawValue) as T;
    } catch (e) {
      if (IS_DEVELOPMENT) {
        //TODO wirklich nur dann?
        console.warn('error while parsing localStorage value of field ' + this.id, e);
      }
      return this.defaultValue;
    }
  }

  private saveToLocalStorage(value: T) {
    const rawValue = JSON.stringify(value);
    localStorage.setItem(this.localStorageKey, rawValue);

    if (IS_DEVELOPMENT) {
      console.log('saved', this.id, rawValue);
    }
  }

  private setValueFromStorage() {
    this.state.value = this.loadFromLocalStorage();
  }
}
