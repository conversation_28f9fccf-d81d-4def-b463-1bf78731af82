#@formatter:off
name: website-build-and-publish
on:
  workflow_dispatch:
    inputs:
      branch:
        description: "The branch to use"
        required: false
        default: "main"
      deploy-to-live:
        type: boolean
        description: "Deploy to live instead of integ?"
        required: false
        default: false
  push:
    branches: [ main ]
    paths:
      - services/website/**
env:
  WORKING_DIR: services/website/
  NODE: '21'

jobs:
  build:
    name: build-and-publish
    runs-on: ubuntu-latest
    steps:
      - name: Store short sha in env
        run: echo "SHORT_SHA=`echo ${GITHUB_SHA} | cut -c1-7`" >> $GITHUB_ENV

      - name: "Checkout code"
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Setup nodeJS for GraphQL Generator
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE }}
          cache: 'yarn'
          cache-dependency-path: ${{ env.WORKING_DIR }}graphql-generator/yarn.lock

      - name: Generate GraphQL DTOs
        working-directory: ${{ env.WORKING_DIR }}graphql-generator
        run: yarn install && yarn generate

      - name: Setup nodeJS for Website
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE }}
          cache: 'yarn'
          cache-dependency-path: ${{ env.WORKING_DIR }}yarn.lock

      - name: Setup vite environment variables
        run: |
          echo "VITE_BUILD_GIT_SHORT_SHA=${{ env.SHORT_SHA }}" >> $GITHUB_ENV
          echo "VITE_BUILD_DATE=$(date -Iseconds)" >> $GITHUB_ENV

      - name: Generate Website and package files into /dist
        working-directory: ${{ env.WORKING_DIR }}
        env:
          NODE_OPTIONS: "--max_old_space_size=4096"
        run: yarn install && yarn build

      - name: Get Commit Message
        run: echo "COMMIT_MESSAGE=Website" >> $GITHUB_ENV
        if: always()

############ DEPLOY to Integ ############

      - name: Authenticate with GCP project that hosts the docker repo
        if: ${{ github.event.inputs.deploy-to-live != 'true' }}
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_INTEG_PROJECT_CLOUD_STORAGE_DEPLOYER_CREDENTIALS }}

      - name: Set up gcloud SDK
        if: ${{ github.event.inputs.deploy-to-live != 'true' }}
        uses: google-github-actions/setup-gcloud@v2

      - name: Syncing website with Cloud storage bucket
        if: ${{ github.event.inputs.deploy-to-live != 'true' }}
        working-directory: ${{ env.WORKING_DIR }}
        run: gsutil -m -h "Cache-Control:private, max-age=900, stale-if-error=86400" rsync -d -c -r dist gs://project-integ.doorbit.com

      - name: Exclude index.html from caching in clients (Cloudflare caches it anyway on the edge)
        if: ${{ github.event.inputs.deploy-to-live != 'true' }}
        working-directory: ${{ env.WORKING_DIR }}
        run: gsutil setmeta -h "Cache-Control:no-store" gs://project-integ.doorbit.com/index.html

      - name: Purge CDN (cache) for website at Cloudflare
        if: ${{ github.event.inputs.deploy-to-live != 'true' }}
        uses: jakejarvis/cloudflare-purge-action@v0.3.0
        env:
          CLOUDFLARE_TOKEN: ${{ secrets.CLOUDFLARE_PURGE_TOKEN }}
          CLOUDFLARE_ZONE: ${{ secrets.CLOUDFLARE_INTEG_ZONE_ID }}
          PURGE_URLS: '["https://project-integ.doorbit.com/index.html"]'

############ Send notification ############

      - name: Google chat notification
        if: ${{ github.event.inputs.deploy-to-live != 'true' }}
        uses: SimonScholz/google-chat-action@main
        with:
          title: Project Configurator (${{ env.COMMIT_MESSAGE }})
          subtitle: https://project-integ.doorbit.com/
          webhookUrl: ${{ secrets.GOOGLE_CHAT_WEBHOOK_URL }}
          jobStatus: ${{ job.status }}
          imageUrl: 'https://fonts.gstatic.com/s/e/notoemoji/15.0/1f916/32.png'
          imageType: SQUARE

# Live deployment if deploy-to-live is true

      - name: Authenticate with GCP project that hosts the docker repo
        if: ${{ github.event.inputs.deploy-to-live == 'true' }}
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GOOGLE_LIVE_PROJECT_CLOUD_STORAGE_DEPLOYER_CREDENTIALS }}

      - name: Set up gcloud SDK
        if: ${{ github.event.inputs.deploy-to-live == 'true' }}
        uses: google-github-actions/setup-gcloud@v2

      - name: Syncing website with Cloud storage bucket
        if: ${{ github.event.inputs.deploy-to-live == 'true' }}
        working-directory: ${{ env.WORKING_DIR }}
        run: gsutil -m -h "Cache-Control:private, max-age=900, stale-if-error=86400" rsync -d -c -r -x "de/*" dist gs://renaldo.doorbit.com

      - name: Exclude index.html from caching in clients (Cloudflare caches it anyway on the edge)
        if: ${{ github.event.inputs.deploy-to-live == 'true' }}
        working-directory: ${{ env.WORKING_DIR }}
        run: gsutil setmeta -h "Cache-Control:no-store" gs://renaldo.doorbit.com/index.html

      - name: Purge CDN (cache) for website at Cloudflare
        if: ${{ github.event.inputs.deploy-to-live == 'true' }}
        uses: jakejarvis/cloudflare-purge-action@v0.3.0
        env:
          CLOUDFLARE_TOKEN: ${{ secrets.CLOUDFLARE_PURGE_TOKEN }}
          CLOUDFLARE_ZONE: ${{ secrets.CLOUDFLARE_INTEG_ZONE_ID }}
          PURGE_URLS: '["https://renaldo.doorbit.com/index.html"]'

