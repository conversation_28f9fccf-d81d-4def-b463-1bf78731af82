query dGetLeistungDetails($leistungId: String!, $configurationId: String!, $displayContext: DisplayContext!) {
  leistungDetails(leistungId: $leistungId, configurationId: $configurationId, displayContext: $displayContext) {
    id
    isSelected
    type
    displayName
    longDisplayName
    longDescription
    imageUrls
    isCustomCreated
    table {
      uValue
      manufacturer
      offerPriceGross
    }
    leistungsPositionen {
      id
      cells {
        key
        booleanValue
        doubleValue
        stringValue
        intValue
      }
    }
  }
}
