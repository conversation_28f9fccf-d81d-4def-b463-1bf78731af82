<template>
  <div class="d-flex align-center mb-4">
    <div>
      <div class="text-subtitle-1 font-weight-bold">{{ t('domain.massedaten') }}</div>
      <div class="text-subtitle-1">{{ t('domain.leistungsposition.takeFromMassedaten') }}</div>
    </div>
    <v-spacer />
    <v-btn class="mr-2" variant="text" :icon="mdiReload" rounded tabindex="-1" @click="refetchMassedaten" />
  </div>
  <template v-if="areMassedatenLoading">
    <v-skeleton-loader type="card" />
    <v-skeleton-loader type="card" />
  </template>
  <template v-else-if="massedaten">
    <v-list :lines="false" nav height="400" tabindex="-1">
      <v-list-item
        v-for="masseIter in massedaten.massedaten"
        :key="masseIter.key"
        tabindex="-1"
        :value="masseIter.key"
        :prepend-icon="masseIter.icon ? (allIcons as Record<string, string>)[masseIter.icon] : mdiHelp"
        @click="() => emits('selected', masseIter.valueDouble ?? masseIter.valueInt ?? null)"
      >
        <v-list-item-title>{{ getTranslation(locale, masseIter.displayName) }}</v-list-item-title>
        <v-list-item-subtitle>{{
          (masseIter.valueDouble ?? masseIter.valueInt ?? masseIter.valueString) + ' ' + masseIter.unitDisplayName
        }}</v-list-item-subtitle>
      </v-list-item>
    </v-list>
  </template>
  <div v-else class="text-pre-wrap text-subtitle-1">
    <div>{{ t('errorViews.noMassedaten') }}</div>
    <div class="font-weight-bold">{{ listingId }}</div>
  </div>
</template>
<script setup lang="ts">
import { mdiHelp, mdiReload } from '@mdi/js';
import * as allIcons from '@mdi/js';
import { getTranslation } from '@/utility/get-translation';
import { useI18n } from 'vue-i18n';
import { useProjectService } from '@/service/use-project-service';
import { useMassedatenService } from '@/service/use-massedaten-service';

const { t, locale } = useI18n();
const { listingId } = useProjectService();
const { areMassedatenLoading, massedaten, refetchMassedaten } = useMassedatenService();

const emits = defineEmits<{ selected: [value: number | null] }>();
</script>
