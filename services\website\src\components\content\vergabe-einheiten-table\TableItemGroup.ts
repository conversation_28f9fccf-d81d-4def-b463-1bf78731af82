import { VergabeEinheitenTableItem } from './VergabeEinheitenTableItem';

export type DataTableHeader<T> = {
  key?: 'data-table-group' | 'data-table-select' | 'data-table-expand' | string;
  value?: SelectItemKey<T>;
  title?: string;
  fixed?: boolean;
  align?: 'start' | 'end' | 'center';
  width?: number | string;
  minWidth?: string;
  maxWidth?: string;
  headerProps?: Record<string, unknown>;
};

export type SelectItemKey<T> =
  | boolean
  | string
  | readonly (string | number)[]
  | ((item: T, fallback?: unknown) => unknown)
  | undefined
  | null;

export type InternalDataTableHeader<T> = Omit<DataTableHeader<T>, 'key' | 'value' | 'children'> & {
  key: string | null;
  value: SelectItemKey<T> | null;
  sortable?: boolean;
  fixedOffset?: number;
  lastFixed?: boolean;
  colspan?: number;
  rowspan?: number;
  children?: InternalDataTableHeader<T>[];
};

export interface TableItemGroup {
  type: 'group' | 'item';
  depth: number;
  id: string;
  key: string;
  value: string;
  items: readonly TableItemGroup[];
  raw?: VergabeEinheitenTableItem;
  columns?: Record<string, string>;
  index?: number;
}
