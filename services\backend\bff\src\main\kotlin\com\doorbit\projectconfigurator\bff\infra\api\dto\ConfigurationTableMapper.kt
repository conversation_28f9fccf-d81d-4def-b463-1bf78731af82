package com.doorbit.projectconfigurator.bff.infra.api.dto

import com.doorbit.projectconfigurator.bff.infra.api.dto.CompletenessState.*
import com.doorbit.projectconfigurator.bff.infra.api.dto.DisplayContext.BACKOFFICE
import com.doorbit.projectconfigurator.bff.infra.api.dto.TranslationMapper.toUiElementTranslation
import com.doorbit.projectconfigurator.projectconfigurator.adapter.outgoing.productdatabase.DbColumnConstants
import com.doorbit.projectconfigurator.projectconfigurator.domain.productdatabase.*
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.ConfiguredLeistungsPosition
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.ProjectConfiguration
import com.doorbit.projectconfigurator.projectconfigurator.domain.projectconfiguration.SelectedLeistung

class ConfigurationTableMapper(private val displayContext: DisplayContext) {

    fun toDto(productDatabase: ProductDatabase, pc: ProjectConfiguration): ConfigurationTable {
        return ConfigurationTable(
            configurationId = pc.id.toHexString(),
            vergabeEinheiten = productDatabase.vergabeEinheiten().map { mapVergabeeinheit(it, pc) },
            calculationResult = mapCalculationResult(pc, productDatabase),
            databaseVersion = mapDatabaseVersion(productDatabase),
            configurationName = pc.name,
        )
    }

    private fun mapDatabaseVersion(productDatabase: ProductDatabase): DatabaseVersion {
        return DatabaseVersion(
            databaseId = productDatabase.id.toHexString(),
            version = productDatabase.versionName,
            createdAt = productDatabase.importedAt.toString(),
        )
    }

    private fun mapCalculationResult(pc: ProjectConfiguration, db: ProductDatabase): CalculationResult {
        return CalculationResult(
            offerPriceGross = pc.calculation.offerPriceGross.toDouble(),
            offerPriceNet = pc.calculation.offerPriceNet.toDouble(),
            vatAmount = pc.calculation.vatAmount.toDouble(),
            calculationsByModule = mapCalculationsByModule(pc, db)
        )
    }

    private fun mapCalculationsByModule(pc: ProjectConfiguration, db: ProductDatabase): List<CalculationByModule> {
        return db.module.distinct().map {
            CalculationByModule(
                moduleName = it.name,
                offerPriceGross = pc.offerPriceGrossByLeistungIds(it.allLeistungIds()).toDouble(),
                offerPriceNet = pc.offerPriceNetByLeistungIds(it.allLeistungIds()).toDouble(),
                vatAmount = pc.vatAmountByLeistungIds(it.allLeistungIds()).toDouble(),
                netCosts = ifDisplayContextIs(BACKOFFICE, pc.netCostsByLeistungIds(it.allLeistungIds()).toDouble(), 0.0),
                margin = ifDisplayContextIs(BACKOFFICE, pc.marginByLeistungIds(it.allLeistungIds()).toDouble(), 0.0),
                securitySurcharge = ifDisplayContextIs(BACKOFFICE, pc.securitySurchargeByLeistungIds(it.allLeistungIds()).toDouble(), 0.0),
            )
        }
    }

    private fun mapVergabeeinheit(it: DbVergabeeinheit, pc: ProjectConfiguration): VergabeEinheit {
        val komponenten = it.komponenten.map { mapKomponente(it, pc) }
        val allLeistungenIds = it.allLeistungIds()
        return VergabeEinheit(
            id = it.recordId,
            displayName = toUiElementTranslation(it.name),
            completenessState = determineComplenessVergabeeinheit(komponenten),
            hasWarnings = false,
            komponenten = komponenten,
            moduleName = it.modul,
            vatAmount = pc.vatAmountByLeistungIds(allLeistungenIds).toDouble(),
            offerPriceNet = pc.offerPriceNetByLeistungIds(allLeistungenIds).toDouble(),
            offerPriceGross = pc.offerPriceGrossByLeistungIds(allLeistungenIds).toDouble(),
            netCosts = ifDisplayContextIs(BACKOFFICE, pc.netCostsByLeistungIds(allLeistungenIds).toDouble(), 0.0),
            margin = ifDisplayContextIs(BACKOFFICE, pc.marginByLeistungIds(allLeistungenIds).toDouble(), 0.0),
            securitySurcharge = ifDisplayContextIs(BACKOFFICE, pc.securitySurchargeByLeistungIds(allLeistungenIds).toDouble(), 0.0),
        )
    }

    private fun determineComplenessVergabeeinheit(komponenten: List<VergabeEinheitKomponente>): CompletenessState {
        return if (komponenten.all { it.completenessState == COMPLETELY_CONFIGURED })
            COMPLETELY_CONFIGURED
        else if (komponenten.none { it.completenessState == NOT_CONFIGURED })
            PARTIALLY_CONFIGURED
        else NOT_CONFIGURED
    }

    private fun mapKomponente(dbKomponente: DbKomponente, pc: ProjectConfiguration): VergabeEinheitKomponente {
        val leistung = leistungFromDb(dbKomponente, pc)
        val allLeistungenIds = dbKomponente.allLeistungIds()
        return VergabeEinheitKomponente(
            id = dbKomponente.recordId,
            displayName = toUiElementTranslation(dbKomponente.name),
            completenessState = determineCompletenessKomponente(leistung),
            hasWarnings = false,
            leistung = leistung,
            vatAmount = pc.vatAmountByLeistungIds(allLeistungenIds).toDouble(),
            offerPriceGross = pc.offerPriceGrossByLeistungIds(allLeistungenIds).toDouble(),
            netCosts = ifDisplayContextIs(BACKOFFICE, pc.netCostsByLeistungIds(allLeistungenIds).toDouble(), 0.0),
            margin = ifDisplayContextIs(BACKOFFICE, pc.marginByLeistungIds(allLeistungenIds).toDouble(), 0.0),
            securitySurcharge = ifDisplayContextIs(BACKOFFICE, pc.securitySurchargeByLeistungIds(allLeistungenIds).toDouble(), 0.0),
        )
    }

    private fun determineCompletenessKomponente(leistung: List<Leistung>): CompletenessState {
        return if (leistung.all { it.isSelected })
            COMPLETELY_CONFIGURED
        else if (leistung.any { it.isSelected })
            PARTIALLY_CONFIGURED
        else NOT_CONFIGURED
    }

    private fun leistungFromDb(dbKomponente: DbKomponente, pc: ProjectConfiguration): List<Leistung> {
        return dbKomponente.dbLeistung.filter { isLeistungDisplayed(it, pc) }.map { mapLeistung(it, pc) }
    }

    private fun isLeistungDisplayed(leistung: DbLeistung, pc: ProjectConfiguration): Boolean {
        if (pc.deletedLeistungen.contains(leistung.recordId)) {
            return false
        }

        if (leistung.customCreatedLeistung == null || leistung.customCreatedLeistung!!.globallyAvailable) {
            return true
        }

        return pc.customCreatedLeistung.contains(leistung.recordId)
    }

    private fun isLeistungsPositionDisplayed(lp: DbLeistungsposition, pc: ProjectConfiguration?): Boolean {
        if (pc?.deletedLeistungsPositionen?.contains(lp.recordId) == true) {
            return false
        }

        if (!lp.isCustomCreatedLeistungsPosition) {
            return true
        }

        return pc?.customCreatedLeistungsPositionen?.contains(lp.recordId) ?: false
    }

    private fun mapLeistung(leistung: DbLeistung, pc: ProjectConfiguration? = null): Leistung {
        val selectedLeistung = pc?.leistungen?.selectedLeistung(leistung.recordId, true)
        return Leistung(
            id = leistung.recordId,
            isSelected = pc?.leistungen?.isLeistungSelected(leistung.recordId) ?: false,
            cells = mapLeistungCells(selectedLeistung, leistung),
            type = leistung.type?.let { LeistungType.valueOf(it.name) } ?: LeistungType.ALTERNATIVE,
            leistungsPositionen = leistung.leistungsPositionen.filter { isLeistungsPositionDisplayed(it, pc) }.map { mapLeistungsposition(selectedLeistung?.configuredLeistungsPosition(it.recordId), leistung.leistungsPosition(it.recordId)) }
        )
    }

    fun mapLeistungsposition(clp: ConfiguredLeistungsPosition?, dbLeistungsposition: DbLeistungsposition): LeistungsPosition {
        return LeistungsPosition(
            id = dbLeistungsposition.recordId,
            cells = mapLeistungspositionCells(clp, dbLeistungsposition)
        )
    }

    private fun mapLeistungspositionCells(clp: ConfiguredLeistungsPosition?, dbLeistungsposition: DbLeistungsposition): List<KeyValueItem> {
        return listOfNotNull(
            KeyValueItem(
                key = "IS_SELECTED",
                stringValue = null,
                booleanValue = clp?.isSelected ?: false,
                doubleValue = null,
                intValue = null
            ),
            KeyValueItem(
                key = "IS_CUSTOM_CREATED",
                stringValue = null,
                booleanValue = dbLeistungsposition.isCustomCreatedLeistungsPosition,
                doubleValue = null,
                intValue = null
            ),
            KeyValueItem(
                key = "DISPLAY_NAME",
                stringValue = dbLeistungsposition.name,
                booleanValue = null,
                doubleValue = null,
                intValue = null
            ),
            KeyValueItem(
                key = "AMOUNT",
                booleanValue = null,
                doubleValue = clp?.quantity?.toDouble() ?: 0.0,
                intValue = null,
                stringValue = null
            ),
            KeyValueItem(
                key = "UNIT",
                booleanValue = null,
                doubleValue = null,
                intValue = null,
                stringValue = dbLeistungsposition.otherFields[DbColumnConstants.UNIT] as? String
            ),
            KeyValueItem(
                key = "CALCULATION_FORMULA",
                booleanValue = null,
                doubleValue = null,
                intValue = null,
                stringValue = dbLeistungsposition.otherFields[DbColumnConstants.CALCULATION_FORMULA] as? String
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "DESCRIPTION",
                booleanValue = null,
                doubleValue = null,
                intValue = null,
                stringValue = dbLeistungsposition.otherFields[DbColumnConstants.DESCRIPTION] as? String
            ),
            KeyValueItem(
                key = "UNIT_PRICE",
                booleanValue = null,
                doubleValue = clp?.unitPrice?.toDouble() ?: dbLeistungsposition.unitPrice.toDouble(),
                intValue = null,
                stringValue = null
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "SECURITY_SURCHARGE",
                booleanValue = null,
                doubleValue = clp?.calculation?.securitySurcharge?.toDouble(),
                intValue = null,
                stringValue = null
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "SECURITY_SURCHARGE_PERCENT",
                booleanValue = null,
                doubleValue = clp?.securitySurchargePercent?.toDouble() ?: dbLeistungsposition.securitySurchargePercent.toDouble(),
                intValue = null,
                stringValue = null
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "MARGIN",
                booleanValue = null,
                doubleValue = clp?.calculation?.margin?.toDouble(),
                intValue = null,
                stringValue = null
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "MARGIN_PERCENT",
                booleanValue = null,
                doubleValue = clp?.marginPercent?.toDouble() ?: dbLeistungsposition.marginPercent.toDouble(),
                intValue = null,
                stringValue = null
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "OFFER_PRICE_NET",
                booleanValue = null,
                doubleValue = clp?.calculation?.offerPriceNet?.toDouble(),
                intValue = null,
                stringValue = null
            ),
            KeyValueItem(
                key = "VAT_RATE",
                booleanValue = null,
                doubleValue = clp?.vatRatePercent?.toDouble() ?: dbLeistungsposition.vatRatePercent.toDouble(),
                intValue = null,
                stringValue = null
            ),
            KeyValueItem(
                key = "VAT_AMOUNT",
                booleanValue = null,
                doubleValue = clp?.calculation?.vatAmount?.toDouble(),
                intValue = null,
                stringValue = null
            ),
            KeyValueItem(
                key = "OFFER_PRICE_GROSS",
                booleanValue = null,
                doubleValue = clp?.calculation?.offerPriceGross?.toDouble(),
                intValue = null,
                stringValue = null
            ),
            KeyValueItem(
                key = "NET_COSTS",
                booleanValue = null,
                doubleValue = clp?.calculation?.netCosts?.toDouble(),
                intValue = null,
                stringValue = null
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "NOTES",
                booleanValue = null,
                doubleValue = null,
                intValue = null,
                stringValue = clp?.notes
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
        )
    }

    private fun mapLeistungCells(leistung: SelectedLeistung? = null, dbLeistung: DbLeistung): List<KeyValueItem> {
        return listOfNotNull(
            KeyValueItem(
              key = "IS_CUSTOM_CREATED",
                stringValue = null,
                booleanValue = dbLeistung.customCreatedLeistung != null,
                doubleValue = null,
                intValue = null
            ),
            KeyValueItem(
                key = "DISPLAY_NAME",
                stringValue = dbLeistung.name,
                booleanValue = null,
                doubleValue = null,
                intValue = null
            ),
            KeyValueItem(
              key = "NET_COSTS",
                stringValue = null,
                booleanValue = null,
                doubleValue = leistung?.totalNetCosts()?.toDouble(),
                intValue = null
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "MARGIN",
                stringValue = null,
                booleanValue = null,
                doubleValue = leistung?.totalMargin()?.toDouble(),
                intValue = null
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "VAT_AMOUNT",
                stringValue = null,
                booleanValue = null,
                doubleValue = leistung?.totalVatAmount()?.toDouble(),
                intValue = null
            ),
            KeyValueItem(
                key = "SECURITY_SURCHARGE",
                stringValue = null,
                booleanValue = null,
                doubleValue = leistung?.totalSecuritySurcharge()?.toDouble(),
                intValue = null
            ).onlyForDisplayContext(BACKOFFICE, this.displayContext),
            KeyValueItem(
                key = "OFFER_PRICE_GROSS",
                booleanValue = null,
                doubleValue = leistung?.totalOfferPriceGross()?.toDouble(),
                intValue = null,
                stringValue = null
            )
        )
    }

    private fun <T> ifDisplayContextIs(displayContext: DisplayContext, thenDisplay: T, otherwiseDisplay: T): T {
        return if (this.displayContext == displayContext) thenDisplay else otherwiseDisplay
    }

    private fun KeyValueItem.onlyForDisplayContext(context: DisplayContext, displayContext: DisplayContext): KeyValueItem? {
        return if (displayContext == context) this else null
    }
}
